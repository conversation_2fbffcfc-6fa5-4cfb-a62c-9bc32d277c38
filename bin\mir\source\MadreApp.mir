[ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 1; @symbol_importdef<0> = [Toybox,1,7,13]; @symbol_importdef<1> = [Application,1,14,25]; ]
import Toybox.Application;
[ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 2; @symbol_importdef<0> = [Toybox,2,7,13]; @symbol_importdef<1> = [Lang,2,14,18]; ]
import Toybox.Lang;
[ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 3; @symbol_importdef<0> = [Toybox,3,7,13]; @symbol_importdef<1> = [WatchUi,3,14,21]; ]
import Toybox.WatchUi;
[ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 5; @symbol_classdef = [MadreApp,5,6,14]; @symbol_extends<0> = [Application,5,23,34]; @symbol_extends<1> = [AppBase,5,35,42]; ]
class MadreApp extends Application.AppBase {
    [ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 5; ]
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 5; ]
    static
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 7; @symbol_functiondef = [initialize,7,13,23]; ]
    function initialize() as Void {
D_Melpa_Madre_source_MadreApp_mc_7_26_9_4_start:
[ "D:\Melpa\Madre\source\MadreApp.mc" 8 8 ]
        symbol [ AppBase %tmp.2 8 8 15 ];
        %tmp.2 = getv ? :AppBase;
        symbol [ initialize %tmp.3 8 16 26 ];
        %tmp.3 = getv function %tmp.2 :initialize;
        invoke %tmp.2 %tmp.3();
D_Melpa_Madre_source_MadreApp_mc_7_26_9_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 12; @symbol_functiondef = [onStart,12,13,20]; @symbol_param<0> = [state,12,21,26]; @symbol_param<0>_type<0> = [Dictionary,12,30,40]; ]
    function onStart(state as Dictionary or Null) as Void {
    }
    [ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 16; @symbol_functiondef = [onStop,16,13,19]; @symbol_param<0> = [state,16,20,25]; @symbol_param<0>_type<0> = [Dictionary,16,29,39]; ]
    function onStop(state as Dictionary or Null) as Void {
    }
    [ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 20; @symbol_functiondef = [getInitialView,20,13,27]; @symbol_return<0> = [Views,20,34,39]; @symbol_return<1> = [Views,20,45,50]; @symbol_return<2> = [InputDelegates,20,52,66]; ]
    function getInitialView() as [Views] or [Views, InputDelegates] {
D_Melpa_Madre_source_MadreApp_mc_20_68_22_4_start:
[ "D:\Melpa\Madre\source\MadreApp.mc" 21 8 ]
        %tmp.1 = newa 1 Array;
        symbol [ Array %tmp.1 21 38 43 ];
        symbol [ MadreView %tmp.5 21 21 30 ];
        %tmp.5 = getv ? :MadreView;
        %tmp.2 = newc %tmp.5 ();
        %tmp.6 = dup %tmp.1;
        %tmp.7 = aputv %tmp.6 0 %tmp.2;
        ret %tmp.7;
D_Melpa_Madre_source_MadreApp_mc_20_68_22_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 25; @symbol_functiondef = [onSettingsChanged,25,13,30]; ]
    function onSettingsChanged() as Void {
D_Melpa_Madre_source_MadreApp_mc_25_41_27_4_start:
[ "D:\Melpa\Madre\source\MadreApp.mc" 26 8 ]
        symbol [ WatchUi %tmp.1 26 8 15 ];
        %tmp.1 = getm $.Toybox.WatchUi;
        symbol [ requestUpdate %tmp.2 26 16 29 ];
        %tmp.2 = getv function %tmp.1 :requestUpdate;
        invoke %tmp.1 %tmp.2();
D_Melpa_Madre_source_MadreApp_mc_25_41_27_4_stop:
    }
}
[ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 31; @symbol_functiondef = [getApp,31,9,15]; @symbol_return<0> = [MadreApp,31,21,29]; ]
function getApp() as MadreApp {
D_Melpa_Madre_source_MadreApp_mc_31_30_33_0_start:
[ "D:\Melpa\Madre\source\MadreApp.mc" 32 4 ]
    symbol [ Application %tmp.1 32 11 22 ];
    %tmp.1 = getm $.Toybox.Application;
    symbol [ getApp %tmp.2 32 23 29 ];
    %tmp.2 = getv function %tmp.1 :getApp;
    %tmp.3 = invoke %tmp.1 %tmp.2();
    %tmp.4 = as %tmp.3 MadreApp;
    symbol [ MadreApp %tmp.4 32 35 43 ];
    ret %tmp.4;
D_Melpa_Madre_source_MadreApp_mc_31_30_33_0_stop:
}
[ @file = "D:\Melpa\Madre\source\MadreApp.mc"; @line = 1; ]
<init> {
}
