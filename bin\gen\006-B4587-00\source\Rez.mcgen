import Toybox.Lang;
using Toybox.WatchUi;
using Toybox.WatchUi as Ui;
using Toybox.Graphics;
using Toybox.Graphics as Gfx;

module Rez {
    module Drawables {
        (:initialized) var LauncherIcon as ResourceId;
    } // Drawables

    module Layouts {
        function WatchFace(dc as Graphics.Dc) as Array<WatchUi.Drawable> {
            var rez_cmp_local_custom_drawable_11939594661139614796 = new Background();

            var rez_cmp_local_text_TimeLabel = new WatchUi.Text({:identifier=>"TimeLabel", :color=>Graphics.COLOR_BLUE, :locX=>(416 * 0.5), :locY=>((416 - dc.getFontHeight(Graphics.FONT_LARGE)) * 0.5), :justification=>Graphics.TEXT_JUSTIFY_CENTER, :font=>Graphics.FONT_LARGE});

            return [rez_cmp_local_custom_drawable_11939594661139614796, rez_cmp_local_text_TimeLabel] as Array<WatchUi.Drawable>;
        }
    } // Layouts

    module Strings {
        (:initialized) var ColorLightGray as ResourceId;
        (:initialized) var ColorBlue as ResourceId;
        (:initialized) var ColorRed as ResourceId;
        (:initialized) var ForegroundColorTitle as ResourceId;
        (:initialized) var BackgroundColorTitle as ResourceId;
        (:initialized) var ColorDarkGray as ResourceId;
        (:initialized) var MilitaryFormatTitle as ResourceId;
        (:initialized) var ColorBlack as ResourceId;
        (:initialized) var ColorWhite as ResourceId;
        (:initialized) var AppName as ResourceId;
    } // Strings

    module Styles {
        module prompt_font_enhanced_readability__body_with_title {
            const exclude as Boolean = true;
        } // prompt_font_enhanced_readability__body_with_title

        module system_size__menu_header {
            const width as Number = 416;
            const height as Number = 141;
        } // system_size__menu_header

        module system_loc__hint_button_left_bottom {
            const x as Number = 0;
            const y as Number = 270;
        } // system_loc__hint_button_left_bottom

        module prompt_font_enhanced_readability__body_no_title {
            const exclude as Boolean = true;
        } // prompt_font_enhanced_readability__body_no_title

        module prompt_loc__body_no_title {
            const x as Number = 42;
            const y as Number = 42;
        } // prompt_loc__body_no_title

        module system_icon_dark__about {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__about.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__about

        module system_icon_light__question {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__question.svg";
            const dithering as String = "none";
        } // system_icon_light__question

        module system_loc__hint_button_left_top {
            const x as Number = 0;
            const y as Number = 73;
        } // system_loc__hint_button_left_top

        module prompt_font__title {
            const justification = Graphics.TEXT_JUSTIFY_CENTER|Graphics.TEXT_JUSTIFY_VCENTER;
            const font = Graphics.FONT_XTINY;
        } // prompt_font__title

        module system_icon_dark__discard {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__discard.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__discard

        module system_size__menu_item {
            const width as Number = 416;
            const height as Number = 134;
        } // system_size__menu_item

        module system_color_dark__text {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // system_color_dark__text

        module system_icon_destructive__discard {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__discard.svg";
            const dithering as String = "none";
        } // system_icon_destructive__discard

        module system_icon_dark__hint_action_menu {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            const dithering as String = "none";
            const automaticPalette as Boolean = true;
        } // system_icon_dark__hint_action_menu

        module prompt_size__title {
            const width as Number = 333;
            const height as Number = 63;
        } // prompt_size__title

        module confirmation_font__body {
            const justification = Graphics.TEXT_JUSTIFY_CENTER|Graphics.TEXT_JUSTIFY_VCENTER;
            const font = Graphics.FONT_TINY;
        } // confirmation_font__body

        module system_color_dark__background {
            const color as Number = 0x000000;
            const background as Number = 0x000000;
        } // system_color_dark__background

        module system_icon_destructive__hint_button_left_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_left_top.svg";
            const dithering as String = "none";
        } // system_icon_destructive__hint_button_left_top

        module system_size__launch_icon {
            const scaleX as Number = 60;
            const scaleY as Number = 60;
            const scaleRelativeTo as String = "screen";
        } // system_size__launch_icon

        module confirmation_input__confirm {
            const button = WatchUi.KEY_ENTER;
        } // confirmation_input__confirm

        module prompt_font__body_with_title {
            const justification = Graphics.TEXT_JUSTIFY_CENTER;
            const font = Graphics.FONT_TINY;
        } // prompt_font__body_with_title

        module confirmation_loc__body {
            const x as Number = 83;
            const y as Number = 83;
        } // confirmation_loc__body

        module system_icon_destructive__hint_button_left_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_left_bottom.svg";
            const dithering as String = "none";
        } // system_icon_destructive__hint_button_left_bottom

        module prompt_color_dark__background {
            const color as Number = 0x000000;
            const background as Number = 0x000000;
        } // prompt_color_dark__background

        module system_icon_destructive__hint_action_menu {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_right_top.svg";
            const dithering as String = "none";
            const automaticPalette as Boolean = true;
        } // system_icon_destructive__hint_action_menu

        module system_loc__hint_action_menu {
            const x as Number = 354;
            const y as Number = 73;
        } // system_loc__hint_action_menu

        module confirmation_icon__hint_confirm {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_completion.svg";
            const dithering as String = "none";
        } // confirmation_icon__hint_confirm

        module system_loc__hint_button_left_middle {
            const x as Number = 0;
            const y as Number = 169;
        } // system_loc__hint_button_left_middle

        module prompt_size__body_no_title {
            const width as Number = 333;
            const height as Number = 333;
        } // prompt_size__body_no_title

        module system_icon_light__hint_button_left_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_top.svg";
            const dithering as String = "none";
        } // system_icon_light__hint_button_left_top

        module prompt_color_light__background {
            const color as Number = 0x000000;
            const background as Number = 0x000000;
        } // prompt_color_light__background

        module confirmation_input__delete {
            const button = WatchUi.KEY_ENTER;
        } // confirmation_input__delete

        module system_icon_dark__hint_button_left_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_top.svg";
            const dithering as String = "none";
        } // system_icon_dark__hint_button_left_top

        module system_input__action_menu {
            const button = WatchUi.KEY_ENTER;
        } // system_input__action_menu

        module system_icon_light__save {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__save.svg";
            const dithering as String = "none";
        } // system_icon_light__save

        module prompt_color_light__body {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // prompt_color_light__body

        module system_loc__subwindow {
            const exclude as Boolean = true;
        } // system_loc__subwindow

        module system_icon_light__warning {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__warning.svg";
            const dithering as String = "none";
        } // system_icon_light__warning

        module confirmation_color_light__body {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // confirmation_color_light__body

        module confirmation_icon_light__hint_keep {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_keep.svg";
            const dithering as String = "none";
        } // confirmation_icon_light__hint_keep

        module activity_color_light__background {
            const color as Number = 0x000000;
            const background as Number = 0x000000;
        } // activity_color_light__background

        module system_icon_positive__hint_button_right_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_right_top.svg";
            const dithering as String = "none";
        } // system_icon_positive__hint_button_right_top

        module system_size__screen {
            const width as Number = 416;
            const height as Number = 416;
        } // system_size__screen

        module system_icon_destructive__hint_button_right_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_right_bottom.svg";
            const dithering as String = "none";
        } // system_icon_destructive__hint_button_right_bottom

        module system_color_light__background {
            const color as Number = 0x000000;
            const background as Number = 0x000000;
        } // system_color_light__background

        module system_icon_destructive__hint_button_right_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_right_top.svg";
            const dithering as String = "none";
        } // system_icon_destructive__hint_button_right_top

        module confirmation_loc__hint_confirm {
            const x as Number = 306;
            const y as Number = 0;
        } // confirmation_loc__hint_confirm

        module activity_color_dark__text {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // activity_color_dark__text

        module confirmation_input__keep {
            const button = WatchUi.KEY_DOWN;
        } // confirmation_input__keep

        module prompt_loc__body_with_title {
            const x as Number = 42;
            const y as Number = 125;
        } // prompt_loc__body_with_title

        module system_icon_dark__search {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__search.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__search

        module activity_color_light__text {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // activity_color_light__text

        module confirmation_icon__hint_keep {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_keep.svg";
            const dithering as String = "none";
        } // confirmation_icon__hint_keep

        module system_icon_dark__save {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__save.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__save

        module system_icon_destructive__hint_button_left_middle {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_left_middle.svg";
            const dithering as String = "none";
        } // system_icon_destructive__hint_button_left_middle

        module system_icon_light__search {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__search.svg";
            const dithering as String = "none";
        } // system_icon_light__search

        module system_icon_dark__hint_button_left_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_bottom.svg";
            const dithering as String = "none";
        } // system_icon_dark__hint_button_left_bottom

        module system_icon_positive__hint_button_left_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_left_top.svg";
            const dithering as String = "none";
        } // system_icon_positive__hint_button_left_top

        module system_loc__hint_button_right_bottom {
            const x as Number = 354;
            const y as Number = 270;
        } // system_loc__hint_button_right_bottom

        module confirmation_icon_dark__hint_reject {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_reject.svg";
            const dithering as String = "none";
        } // confirmation_icon_dark__hint_reject

        module confirmation_color_dark__body {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // confirmation_color_dark__body

        module confirmation_icon_light__hint_confirm {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_completion.svg";
            const dithering as String = "none";
        } // confirmation_icon_light__hint_confirm

        module prompt_color_dark__title {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // prompt_color_dark__title

        module system_icon_destructive__cancel {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__cancel.svg";
            const dithering as String = "none";
        } // system_icon_destructive__cancel

        module system_icon_light__hint_button_right_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_bottom.svg";
            const dithering as String = "none";
        } // system_icon_light__hint_button_right_bottom

        module prompt_loc__title {
            const x as Number = 42;
            const y as Number = 31;
        } // prompt_loc__title

        module confirmation_font_enhanced_readability__body {
            const exclude as Boolean = true;
        } // confirmation_font_enhanced_readability__body

        module prompt_color_light__title {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // prompt_color_light__title

        module system_icon_dark__hint_button_right_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            const dithering as String = "none";
        } // system_icon_dark__hint_button_right_top

        module system_icon_dark__hint_button_left_middle {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_middle.svg";
            const dithering as String = "none";
        } // system_icon_dark__hint_button_left_middle

        module confirmation_icon_dark__hint_keep {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_keep.svg";
            const dithering as String = "none";
        } // confirmation_icon_dark__hint_keep

        module system_icon_light__cancel {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__cancel.svg";
            const dithering as String = "none";
        } // system_icon_light__cancel

        module confirmation_icon_light__hint_reject {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_reject.svg";
            const dithering as String = "none";
        } // confirmation_icon_light__hint_reject

        module system_icon_light__about {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__about.svg";
            const dithering as String = "none";
        } // system_icon_light__about

        module system_size__toast_icon {
            const scaleX as Number = 40;
            const scaleY as Number = 40;
            const scaleRelativeTo as String = "screen";
        } // system_size__toast_icon

        module system_icon_dark__hint_button_right_middle {
            const exclude as Boolean = true;
        } // system_icon_dark__hint_button_right_middle

        module confirmation_icon__hint_reject {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_reject.svg";
            const dithering as String = "none";
        } // confirmation_icon__hint_reject

        module system_icon_light__hint_action_menu {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            const dithering as String = "none";
            const automaticPalette as Boolean = true;
        } // system_icon_light__hint_action_menu

        module system_icon_light__hint_button_right_middle {
            const exclude as Boolean = true;
        } // system_icon_light__hint_button_right_middle

        module system_icon_destructive__hint_button_right_middle {
            const exclude as Boolean = true;
        } // system_icon_destructive__hint_button_right_middle

        module confirmation_loc__hint_reject {
            const x as Number = 0;
            const y as Number = 256;
            const horizontalJustification as String = "left";
        } // confirmation_loc__hint_reject

        module confirmation_icon_dark__hint_delete {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_delete.svg";
            const dithering as String = "none";
        } // confirmation_icon_dark__hint_delete

        module activity_color_dark__background {
            const color as Number = 0x000000;
            const background as Number = 0x000000;
        } // activity_color_dark__background

        module system_icon_light__hint_button_left_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_bottom.svg";
            const dithering as String = "none";
        } // system_icon_light__hint_button_left_bottom

        module system_icon_positive__hint_button_left_middle {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_left_middle.svg";
            const dithering as String = "none";
        } // system_icon_positive__hint_button_left_middle

        module system_size__menu_icon {
            const scaleX as Number = 60;
            const scaleY as Number = 60;
            const scaleRelativeTo as String = "screen";
        } // system_size__menu_icon

        module system_icon_destructive__warning {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__warning.svg";
            const dithering as String = "none";
        } // system_icon_destructive__warning

        module system_loc__hint_button_right_middle {
            const exclude as Boolean = true;
        } // system_loc__hint_button_right_middle

        module system_icon_dark__cancel {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__cancel.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__cancel

        module prompt_size__body_with_title {
            const width as Number = 333;
            const height as Number = 250;
        } // prompt_size__body_with_title

        module system_loc__hint_button_right_top {
            const x as Number = 354;
            const y as Number = 73;
        } // system_loc__hint_button_right_top

        module system_color_light__text {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // system_color_light__text

        module system_icon_light__check {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__check.svg";
            const dithering as String = "none";
        } // system_icon_light__check

        module confirmation_input__reject {
            const button = WatchUi.KEY_DOWN;
        } // confirmation_input__reject

        module system_icon_light__revert {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__revert.svg";
            const dithering as String = "none";
        } // system_icon_light__revert

        module system_size__subwindow {
            const exclude as Boolean = true;
        } // system_size__subwindow

        module system_icon_positive__hint_button_left_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_left_bottom.svg";
            const dithering as String = "none";
        } // system_icon_positive__hint_button_left_bottom

        module system_icon_dark__check {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__check.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__check

        module confirmation_loc__hint_keep {
            const x as Number = 0;
            const y as Number = 256;
            const horizontalJustification as String = "left";
        } // confirmation_loc__hint_keep

        module confirmation_loc__hint_delete {
            const x as Number = 306;
            const y as Number = 0;
        } // confirmation_loc__hint_delete

        module system_icon_positive__hint_action_menu {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_right_top.svg";
            const dithering as String = "none";
            const automaticPalette as Boolean = true;
        } // system_icon_positive__hint_action_menu

        module system_icon_positive__hint_button_right_middle {
            const exclude as Boolean = true;
        } // system_icon_positive__hint_button_right_middle

        module system_icon_light__hint_button_left_middle {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_middle.svg";
            const dithering as String = "none";
        } // system_icon_light__hint_button_left_middle

        module system_icon_dark__hint_button_right_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_bottom.svg";
            const dithering as String = "none";
        } // system_icon_dark__hint_button_right_bottom

        module system_icon_positive__hint_button_right_bottom {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_right_bottom.svg";
            const dithering as String = "none";
        } // system_icon_positive__hint_button_right_bottom

        module system_icon_dark__revert {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__revert.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__revert

        module confirmation_icon_light__hint_delete {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_delete.svg";
            const dithering as String = "none";
        } // confirmation_icon_light__hint_delete

        module system_icon_dark__question {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__question.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__question

        module system_icon_light__discard {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__discard.svg";
            const dithering as String = "none";
        } // system_icon_light__discard

        module device_info {
            const screenWidth as Number = 416;
            const screenShape = Toybox.System.SCREEN_SHAPE_ROUND;
            const hasNightMode as Boolean = false;
            const hasGpu as Boolean = true;
            const screenHeight as Number = 416;
            const hasTouchScreen as Boolean = false;
            const hasEnhancedReadabilityMode as Boolean = false;
        } // device_info

        module confirmation_icon_dark__hint_confirm {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_completion.svg";
            const dithering as String = "none";
        } // confirmation_icon_dark__hint_confirm

        module prompt_font__body_no_title {
            const justification = Graphics.TEXT_JUSTIFY_CENTER|Graphics.TEXT_JUSTIFY_VCENTER;
            const font = Graphics.FONT_TINY;
        } // prompt_font__body_no_title

        module prompt_size__title_icon {
            const scaleX as Float = 0.15;
            const scaleRelativeTo as String = "screen";
        } // prompt_size__title_icon

        module system_icon_dark__warning {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__warning.svg";
            const compress as String = "true";
            const dithering as String = "none";
            const automaticPalette as String = "true";
        } // system_icon_dark__warning

        module prompt_loc__title_icon {
            const x as Number = 208;
            const y as Number = 63;
            const horizontalJustification as String = "center";
            const verticalJustification as String = "center";
        } // prompt_loc__title_icon

        module prompt_font_enhanced_readability__title {
            const exclude as Boolean = true;
        } // prompt_font_enhanced_readability__title

        module prompt_color_dark__body {
            const color as Number = 0xFFFFFF;
            const background = Graphics.COLOR_TRANSPARENT;
        } // prompt_color_dark__body

        module confirmation_icon__hint_delete {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_delete.svg";
            const dithering as String = "none";
        } // confirmation_icon__hint_delete

        module system_icon_light__hint_button_right_top {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            const dithering as String = "none";
        } // system_icon_light__hint_button_right_top

        module confirmation_size__body {
            const width as Number = 250;
            const height as Number = 250;
        } // confirmation_size__body

        module system_icon_positive__check {
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__check.svg";
            const dithering as String = "none";
        } // system_icon_positive__check
    } // Styles
} // Rez
