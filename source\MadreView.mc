import Toybox.Graphics;
import Toybox.Lang;
import Toybox.System;
import Toybox.WatchUi;
import Toybox.ActivityMonitor;
import Toybox.Time;
import Toybox.Time.Gregorian;
import Toybox.UserProfile;
import Toybox.Math;

class MadreView extends WatchUi.WatchFace {
    private var _screenCenterPoint as Array<Number> = [0, 0];
    private var _screenWidth as Number = 0;
    private var _screenHeight as Number = 0;
    private var _partialUpdatesAllowed as <PERSON>olean;
    private var _isAwake as Boolean = false; // Initialize with false

    // Colors
    private var _backgroundColor as Number = 0;
    private var _foregroundColor as Number = 0;
    private var _accentColor as Number = 0;

    // Fonts
    private var _timeFont as Graphics.VectorFont?;
    private var _dataFont as Graphics.VectorFont?;
    private var _smallFont as Graphics.VectorFont?;



    function initialize() {
        WatchFace.initialize();
         _partialUpdatesAllowed = (WatchUi.WatchFace has :onPartialUpdate);
        _isAwake = true;
    }

    // Load your resources here
    function onLayout(dc as Dc) as Void {
        _screenWidth = dc.getWidth();
        _screenHeight = dc.getHeight();
        _screenCenterPoint = [_screenWidth / 2, _screenHeight / 2];

        // Initialize colors based on system theme
        var deviceSettings = System.getDeviceSettings();
        if (deviceSettings.requiresBurnInProtection) {
            _backgroundColor = Graphics.COLOR_BLACK;
            _foregroundColor = Graphics.COLOR_WHITE;
            _accentColor = Graphics.COLOR_BLUE;
        } else {
            _backgroundColor = Graphics.COLOR_BLACK;
            _foregroundColor = Graphics.COLOR_WHITE;
            _accentColor = Graphics.COLOR_BLUE;
        }

        // Initialize fonts
        _timeFont = Graphics.getVectorFont({:face=>"RobotoCondensed", :size=>72});
        _dataFont = Graphics.getVectorFont({:face=>"RobotoCondensed", :size=>24});
        _smallFont = Graphics.getVectorFont({:face=>"RobotoCondensed", :size=>18});
    }

    // Called when this View is brought to the foreground. Restore
    // the state of this View and prepare it to be shown. This includes
    // loading resources into memory.
    function onShow() as Void {
    }

    // Update the view
    function onUpdate(dc as Dc) as Void {
        var targetDc = null;

        if (_partialUpdatesAllowed) {
            targetDc = dc;
        } else {
            targetDc = dc;
        }

        var clockTime = System.getClockTime();
        var date = Gregorian.info(Time.now(), Time.FORMAT_MEDIUM);

        // Clear screen
        targetDc.setColor(_backgroundColor, _backgroundColor);
        targetDc.clear();

        // Draw main time
        drawTime(targetDc, clockTime);

        // Draw date
        drawDate(targetDc, date);

        // Only show detailed information when the watch is awake
        if (_isAwake) {
            // Draw activity data
            drawActivityData(targetDc);

            // Draw battery indicator
            drawBattery(targetDc);

            // Draw heart rate
            drawHeartRate(targetDc);

            // Draw steps
            drawSteps(targetDc);
        }

        if (_partialUpdatesAllowed) {
            onPartialUpdate(targetDc);
        }
    }

    // Called when this View is removed from the screen. Save the
    // state of this View here. This includes freeing resources from
    // memory.
    function onHide() as Void {
    }

    // The user has just looked at their watch. Timers and animations may be started here.
    function onExitSleep() as Void {
        _isAwake = true;
        WatchUi.requestUpdate();
    }

    // Terminate any active timers and prepare for slow updates.
    function onEnterSleep() as Void {
         _isAwake = false;
        WatchUi.requestUpdate();
    }

    private function drawTime(dc as Graphics.Dc, clockTime as System.ClockTime) as Void {
        var timeString;
        var is24Hour = System.getDeviceSettings().is24Hour;

        if (is24Hour) {
            timeString = Lang.format("$1$:$2$", [
                clockTime.hour.format("%02d"),
                clockTime.min.format("%02d")
            ]);
        } else {
            var hour = clockTime.hour;
            if (hour > 12) {
                hour = hour - 12;
            } else if (hour == 0) {
                hour = 12;
            }
            timeString = Lang.format("$1$:$2$", [
                hour.format("%d"),
                clockTime.min.format("%02d")
            ]);
        }

        dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
        var timeTextDimensions = dc.getTextDimensions(timeString, _timeFont);
        var timeX = (_screenWidth - timeTextDimensions[0]) / 2;
        var timeY = (_screenHeight - timeTextDimensions[1]) / 2 - 20;

        dc.drawText(timeX, timeY, _timeFont, timeString, Graphics.TEXT_JUSTIFY_LEFT);
    }

     private function drawDate(dc as Graphics.Dc, date as Gregorian.Info) as Void {
        var dateString = Lang.format("$1$ $2$", [
            date.day_of_week.toUpper(),
            date.day
        ]);

        dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
        var dateX = _screenCenterPoint[0];
        var dateY = _screenCenterPoint[1] + 40;

        dc.drawText(dateX, dateY, _dataFont, dateString, Graphics.TEXT_JUSTIFY_CENTER);
    }

    private function drawActivityData(dc as Graphics.Dc) as Void {
        var activityInfo = ActivityMonitor.getInfo();
        var yOffset = 80;

        // Steps
        if (activityInfo has :steps && activityInfo.steps != null) {
            var stepsString = activityInfo.steps.toString();
            dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(_screenWidth - 10, yOffset, _smallFont, stepsString, Graphics.TEXT_JUSTIFY_RIGHT);

            // Steps icon (simplified)
            dc.setColor(_accentColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(_screenWidth - 50, yOffset, _smallFont, "👣", Graphics.TEXT_JUSTIFY_RIGHT);
        }

        // Calories
        if (activityInfo has :calories && activityInfo.calories != null) {
            var caloriesString = activityInfo.calories.toString();
            dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(_screenWidth - 10, yOffset + 25, _smallFont, caloriesString, Graphics.TEXT_JUSTIFY_RIGHT);

            // Calories icon
            dc.setColor(_accentColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(_screenWidth - 50, yOffset + 25, _smallFont, "🔥", Graphics.TEXT_JUSTIFY_RIGHT);
        }
    }

     private function drawBattery(dc as Graphics.Dc) as Void {
        var battery = System.getSystemStats().battery;
        var batteryString = Lang.format("$1$%", [battery.format("%.0f")]);

        dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
        dc.drawText(10, 80, _smallFont, batteryString, Graphics.TEXT_JUSTIFY_LEFT);

        // Battery icon
        dc.setColor(_accentColor, Graphics.COLOR_TRANSPARENT);
        var batteryWidth = 20;
        var batteryHeight = 10;
        var batteryX = 10;
        var batteryY = 100;

        // Battery outline
        dc.drawRectangle(batteryX, batteryY, batteryWidth, batteryHeight);
        dc.drawRectangle(batteryX + batteryWidth, batteryY + 2, 3, batteryHeight - 4);

        // Battery fill
        var fillWidth = (batteryWidth - 2) * battery / 100;
        if (battery > 20) {
            dc.setColor(Graphics.COLOR_GREEN, Graphics.COLOR_TRANSPARENT);
        } else {
            dc.setColor(Graphics.COLOR_RED, Graphics.COLOR_TRANSPARENT);
        }
        dc.fillRectangle(batteryX + 1, batteryY + 1, fillWidth, batteryHeight - 2);
    }

    private function drawHeartRate(dc as Graphics.Dc) as Void {
        var activityInfo = ActivityMonitor.getInfo();

        if (activityInfo has :currentHeartRate && activityInfo.currentHeartRate != null) {
            var hrString = activityInfo.currentHeartRate.toString();
            dc.setColor(_foregroundColor, Graphics.COLOR_TRANSPARENT);
            dc.drawText(_screenCenterPoint[0] + 50, 30, _smallFont, hrString, Graphics.TEXT_JUSTIFY_LEFT);

            // Heart icon
            dc.setColor(Graphics.COLOR_RED, Graphics.COLOR_TRANSPARENT);
            dc.drawText(_screenCenterPoint[0] + 20, 30, _smallFont, "♥", Graphics.TEXT_JUSTIFY_LEFT);
        }
    }

    private function drawSteps(dc as Graphics.Dc) as Void {
        var activityInfo = ActivityMonitor.getInfo();

        if (activityInfo has :steps && activityInfo.steps != null) {
            var goal = activityInfo.stepGoal;
            if (goal != null && goal > 0) {
                var progress = activityInfo.steps.toFloat() / goal.toFloat();
                if (progress > 1.0) {
                    progress = 1.0;
                }

                // Draw progress arc
                var centerX = _screenCenterPoint[0];
                var centerY = _screenCenterPoint[1];
                var radius = 90;
                var startAngle = -90; // Start from top
                var sweepAngle = 360 * progress;

                dc.setColor(_accentColor, Graphics.COLOR_TRANSPARENT);
                dc.setPenWidth(4);

                // Draw background arc
                dc.setColor(Graphics.COLOR_DK_GRAY, Graphics.COLOR_TRANSPARENT);
                drawArc(dc, centerX, centerY, radius, -90, 360);

                // Draw progress arc
                dc.setColor(_accentColor, Graphics.COLOR_TRANSPARENT);
                drawArc(dc, centerX, centerY, radius, startAngle, sweepAngle.toNumber());
            }
        }
    }

    private function drawArc(dc as Graphics.Dc, centerX as Number, centerY as Number, radius as Number,
                           startAngle as Number, sweepAngle as Number) as Void {
        var segments = 36; // Number of line segments to approximate the arc
        var angleStep = sweepAngle / segments;

        for (var i = 0; i < segments; i++) {
            var angle1 = Math.toRadians(startAngle + i * angleStep);
            var angle2 = Math.toRadians(startAngle + (i + 1) * angleStep);

            var x1 = centerX + radius * Math.cos(angle1);
            var y1 = centerY + radius * Math.sin(angle1);
            var x2 = centerX + radius * Math.cos(angle2);
            var y2 = centerY + radius * Math.sin(angle2);

            dc.drawLine(x1, y1, x2, y2);
        }
    }

}
