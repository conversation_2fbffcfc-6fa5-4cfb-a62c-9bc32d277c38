[ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 1; @symbol_importdef<0> = [Toybox,1,7,13]; @symbol_importdef<1> = [Application,1,14,25]; @symbol_importdef<2> = [Storage,1,26,33]; ]
import Toybox.Application.Storage;
[ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 2; @symbol_importdef<0> = [Toybox,2,7,13]; @symbol_importdef<1> = [Graphics,2,14,22]; ]
import Toybox.Graphics;
[ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 3; @symbol_importdef<0> = [Toybox,3,7,13]; @symbol_importdef<1> = [WatchUi,3,14,21]; ]
import Toybox.WatchUi;
[ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 5; @symbol_classdef = [WatchFaceSettingsMenu,5,6,27]; @symbol_extends<0> = [WatchUi,5,36,43]; @symbol_extends<1> = [Menu2,5,44,49]; ]
class WatchFaceSettingsMenu extends WatchUi.Menu2 {
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 5; ]
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 5; ]
    static
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 7; @symbol_functiondef = [initialize,7,13,23]; ]
    function initialize() as Void {
D_Melpa_Madre_source_WatchFaceSettings_mc_7_26_30_4_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 8 8 ]
        symbol [ Menu2 %tmp.2 8 8 13 ];
        %tmp.2 = getv ? :Menu2;
        symbol [ initialize %tmp.3 8 14 24 ];
        %tmp.3 = getv function %tmp.2 :initialize;
        %tmp.4 = newd 1;
        %tmp.6 = const :title;
        symbol [ title %tmp.6 8 27 32 const ];
        %tmp.7 = "Settings";
        %tmp.8 = dup %tmp.4;
        %tmp.9 = aputv %tmp.8 %tmp.6 %tmp.7;
        invoke %tmp.2 %tmp.3(%tmp.9);
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 10 8 ]
        symbol [ Menu2 %tmp.11 10 8 13 ];
        %tmp.11 = getv ? :Menu2;
        symbol [ addItem %tmp.12 10 14 21 ];
        %tmp.12 = getv function %tmp.11 :addItem;
        symbol [ WatchUi %tmp.15 10 26 33 ];
        %tmp.15 = getm $.Toybox.WatchUi;
        symbol [ MenuItem %tmp.16 10 34 42 ];
        %tmp.16 = getv function ? %tmp.15 :MenuItem;
        %tmp.17 = "Theme";
        %tmp.18 = "Light/Dark";
        %tmp.19 = "theme";
        %tmp.20 = null;
        %tmp.13 = newc %tmp.16 (%tmp.17, %tmp.18, %tmp.19, %tmp.20);
        invoke %tmp.11 %tmp.12(%tmp.13);
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 17 8 ]
        symbol [ Menu2 %tmp.22 17 8 13 ];
        %tmp.22 = getv ? :Menu2;
        symbol [ addItem %tmp.23 17 14 21 ];
        %tmp.23 = getv function %tmp.22 :addItem;
        symbol [ WatchUi %tmp.26 17 26 33 ];
        %tmp.26 = getm $.Toybox.WatchUi;
        symbol [ MenuItem %tmp.27 17 34 42 ];
        %tmp.27 = getv function ? %tmp.26 :MenuItem;
        %tmp.28 = "Show Seconds";
        %tmp.29 = "On/Off";
        %tmp.30 = "seconds";
        %tmp.31 = null;
        %tmp.24 = newc %tmp.27 (%tmp.28, %tmp.29, %tmp.30, %tmp.31);
        invoke %tmp.22 %tmp.23(%tmp.24);
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 24 8 ]
        symbol [ Menu2 %tmp.33 24 8 13 ];
        %tmp.33 = getv ? :Menu2;
        symbol [ addItem %tmp.34 24 14 21 ];
        %tmp.34 = getv function %tmp.33 :addItem;
        symbol [ WatchUi %tmp.37 24 26 33 ];
        %tmp.37 = getm $.Toybox.WatchUi;
        symbol [ MenuItem %tmp.38 24 34 42 ];
        %tmp.38 = getv function ? %tmp.37 :MenuItem;
        %tmp.39 = "Data Fields";
        %tmp.40 = "Customize";
        %tmp.41 = "datafields";
        %tmp.42 = null;
        %tmp.35 = newc %tmp.38 (%tmp.39, %tmp.40, %tmp.41, %tmp.42);
        invoke %tmp.33 %tmp.34(%tmp.35);
D_Melpa_Madre_source_WatchFaceSettings_mc_7_26_30_4_stop:
    }
}
[ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 33; @symbol_classdef = [WatchFaceSettingsDelegate,33,6,31]; @symbol_extends<0> = [WatchUi,33,40,47]; @symbol_extends<1> = [Menu2InputDelegate,33,48,66]; ]
class WatchFaceSettingsDelegate extends WatchUi.Menu2InputDelegate {
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 33; ]
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 33; ]
    static
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 35; @symbol_functiondef = [initialize,35,13,23]; ]
    function initialize() as Void {
D_Melpa_Madre_source_WatchFaceSettings_mc_35_26_37_4_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 36 8 ]
        symbol [ Menu2InputDelegate %tmp.2 36 8 26 ];
        %tmp.2 = getv ? :Menu2InputDelegate;
        symbol [ initialize %tmp.3 36 27 37 ];
        %tmp.3 = getv function %tmp.2 :initialize;
        invoke %tmp.2 %tmp.3();
D_Melpa_Madre_source_WatchFaceSettings_mc_35_26_37_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 39; @symbol_functiondef = [onSelect,39,13,21]; @symbol_param<0> = [item,39,22,26]; @symbol_param<0>_type<0> = [WatchUi,39,30,37]; @symbol_param<0>_type<1> = [MenuItem,39,38,46]; ]
    function onSelect(item as WatchUi.MenuItem) as Void {
D_Melpa_Madre_source_WatchFaceSettings_mc_39_56_51_4_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 40 8 ]
        [ "D_Melpa_Madre_source_WatchFaceSettings_mc_39_56_51_4_start" "D_Melpa_Madre_source_WatchFaceSettings_mc_39_56_51_4_stop" ]
        %id.1 = local;
        symbol [ id %id.1 40 12 14 ];
        %tmp.1 = lgetv %item;
        symbol [ item %tmp.1 40 17 21 ];
        symbol [ getId %tmp.2 40 22 27 ];
        %tmp.2 = getv function %tmp.1 :getId;
        %tmp.3 = invoke %tmp.1 %tmp.2();
        lputv %id.1 %tmp.3;
        symbol [ id %id.1 40 12 14 ];
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 42 8 ]
D_Melpa_Madre_source_WatchFaceSettings_mc_42_8_48_8_if_stmt:
        %tmp.4 = lgetv %id.1;
        symbol [ id %tmp.4 42 12 14 ];
        symbol [ equals %tmp.5 42 15 21 ];
        %tmp.5 = getv function %tmp.4 :equals;
        %tmp.6 = "theme";
        %tmp.7 = invoke %tmp.4 %tmp.5(%tmp.6);
        bf %tmp.7 @D_Melpa_Madre_source_WatchFaceSettings_mc_42_8_48_8_if_false;
D_Melpa_Madre_source_WatchFaceSettings_mc_42_8_48_8_if_true:
D_Melpa_Madre_source_WatchFaceSettings_mc_42_32_44_8_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 43 12 ]
        %tmp.8 = self;
        symbol [ toggleTheme %tmp.9 43 12 23 ];
        %tmp.9 = getv function %tmp.8 :toggleTheme;
        invoke %tmp.8 %tmp.9();
D_Melpa_Madre_source_WatchFaceSettings_mc_42_32_44_8_stop:
        goto @D_Melpa_Madre_source_WatchFaceSettings_mc_42_8_48_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_42_8_48_8_if_false:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 44 15 ]
D_Melpa_Madre_source_WatchFaceSettings_mc_44_15_48_8_if_stmt:
        %tmp.10 = lgetv %id.1;
        symbol [ id %tmp.10 44 19 21 ];
        symbol [ equals %tmp.11 44 22 28 ];
        %tmp.11 = getv function %tmp.10 :equals;
        %tmp.12 = "seconds";
        %tmp.13 = invoke %tmp.10 %tmp.11(%tmp.12);
        bf %tmp.13 @D_Melpa_Madre_source_WatchFaceSettings_mc_44_15_48_8_if_false;
D_Melpa_Madre_source_WatchFaceSettings_mc_44_15_48_8_if_true:
D_Melpa_Madre_source_WatchFaceSettings_mc_44_41_46_8_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 45 12 ]
        %tmp.14 = self;
        symbol [ toggleSeconds %tmp.15 45 12 25 ];
        %tmp.15 = getv function %tmp.14 :toggleSeconds;
        invoke %tmp.14 %tmp.15();
D_Melpa_Madre_source_WatchFaceSettings_mc_44_41_46_8_stop:
        goto @D_Melpa_Madre_source_WatchFaceSettings_mc_44_15_48_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_44_15_48_8_if_false:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 46 15 ]
D_Melpa_Madre_source_WatchFaceSettings_mc_46_15_48_8_if_stmt:
        %tmp.16 = lgetv %id.1;
        symbol [ id %tmp.16 46 19 21 ];
        symbol [ equals %tmp.17 46 22 28 ];
        %tmp.17 = getv function %tmp.16 :equals;
        %tmp.18 = "datafields";
        %tmp.19 = invoke %tmp.16 %tmp.17(%tmp.18);
        bf %tmp.19 @D_Melpa_Madre_source_WatchFaceSettings_mc_46_15_48_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_46_15_48_8_if_true:
D_Melpa_Madre_source_WatchFaceSettings_mc_46_44_48_8_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 47 12 ]
        %tmp.20 = self;
        symbol [ customizeDataFields %tmp.21 47 12 31 ];
        %tmp.21 = getv function %tmp.20 :customizeDataFields;
        invoke %tmp.20 %tmp.21();
D_Melpa_Madre_source_WatchFaceSettings_mc_46_44_48_8_stop:
        goto @D_Melpa_Madre_source_WatchFaceSettings_mc_46_15_48_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_46_15_48_8_if_end:
D_Melpa_Madre_source_WatchFaceSettings_mc_44_15_48_8_if_end:
D_Melpa_Madre_source_WatchFaceSettings_mc_42_8_48_8_if_end:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 50 8 ]
        symbol [ WatchUi %tmp.22 50 8 15 ];
        %tmp.22 = getm $.Toybox.WatchUi;
        symbol [ popView %tmp.23 50 16 23 ];
        %tmp.23 = getv function %tmp.22 :popView;
        symbol [ WatchUi %tmp.24 50 24 31 ];
        %tmp.24 = getm $.Toybox.WatchUi;
        symbol [ SLIDE_IMMEDIATE %tmp.25 50 32 47 ];
        %tmp.25 = getv %tmp.24 :SLIDE_IMMEDIATE;
        invoke %tmp.22 %tmp.23(%tmp.25);
D_Melpa_Madre_source_WatchFaceSettings_mc_39_56_51_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 53; @symbol_functiondef = [toggleTheme,53,21,32]; ]
    private
    function toggleTheme() as Void {
D_Melpa_Madre_source_WatchFaceSettings_mc_53_43_61_4_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 54 8 ]
        [ "D_Melpa_Madre_source_WatchFaceSettings_mc_53_43_61_4_start" "D_Melpa_Madre_source_WatchFaceSettings_mc_53_43_61_4_stop" ]
        %currentTheme.1 = local;
        symbol [ currentTheme %currentTheme.1 54 12 24 ];
        symbol [ Storage %tmp.1 54 27 34 ];
        %tmp.1 = getm $.Toybox.Application.Storage;
        symbol [ getValue %tmp.2 54 35 43 ];
        %tmp.2 = getv function %tmp.1 :getValue;
        %tmp.3 = "theme";
        %tmp.4 = invoke %tmp.1 %tmp.2(%tmp.3);
        lputv %currentTheme.1 %tmp.4;
        symbol [ currentTheme %currentTheme.1 54 12 24 ];
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 55 8 ]
D_Melpa_Madre_source_WatchFaceSettings_mc_55_8_57_8_if_stmt:
        %tmp.5 = lgetv %currentTheme.1;
        symbol [ currentTheme %tmp.5 55 12 24 ];
        %tmp.6 = null;
        %tmp.7 = eq %tmp.5 %tmp.6;
        bf %tmp.7 @D_Melpa_Madre_source_WatchFaceSettings_mc_55_8_57_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_55_8_57_8_if_true:
D_Melpa_Madre_source_WatchFaceSettings_mc_55_34_57_8_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 56 12 ]
        %tmp.8 = "dark";
        lputv %currentTheme.1 %tmp.8;
        symbol [ currentTheme %currentTheme.1 56 12 24 ];
D_Melpa_Madre_source_WatchFaceSettings_mc_55_34_57_8_stop:
        goto @D_Melpa_Madre_source_WatchFaceSettings_mc_55_8_57_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_55_8_57_8_if_end:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 59 8 ]
        [ "D_Melpa_Madre_source_WatchFaceSettings_mc_53_43_61_4_start" "D_Melpa_Madre_source_WatchFaceSettings_mc_53_43_61_4_stop" ]
        %newTheme.2 = local;
        symbol [ newTheme %newTheme.2 59 12 20 ];
D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_begin:
        %tmp.9 = lgetv %currentTheme.1;
        symbol [ currentTheme %tmp.9 59 23 35 ];
        symbol [ equals %tmp.10 59 36 42 ];
        %tmp.10 = getv function %tmp.9 :equals;
        %tmp.11 = "dark";
        %tmp.12 = invoke %tmp.9 %tmp.10(%tmp.11);
        bf %tmp.12 @D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_false;
D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_true:
        %tmp.13 = "light";
        push %tmp.13;
        goto @D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_false:
        %tmp.14 = "dark";
        push %tmp.14;
D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_end:
        %tmp.15 = phi [%tmp.12 @D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_begin] [%tmp.13 @D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_true] [%tmp.14 @D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_false] [%tmp.15 @D_Melpa_Madre_source_WatchFaceSettings_mc_59_23_59_63_end];
        lputv %newTheme.2 %tmp.15;
        symbol [ newTheme %newTheme.2 59 12 20 ];
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 60 8 ]
        symbol [ Storage %tmp.16 60 8 15 ];
        %tmp.16 = getm $.Toybox.Application.Storage;
        symbol [ setValue %tmp.17 60 16 24 ];
        %tmp.17 = getv function %tmp.16 :setValue;
        %tmp.18 = "theme";
        %tmp.19 = lgetv %newTheme.2;
        symbol [ newTheme %tmp.19 60 34 42 ];
        invoke %tmp.16 %tmp.17(%tmp.18, %tmp.19);
D_Melpa_Madre_source_WatchFaceSettings_mc_53_43_61_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 63; @symbol_functiondef = [toggleSeconds,63,21,34]; ]
    private
    function toggleSeconds() as Void {
D_Melpa_Madre_source_WatchFaceSettings_mc_63_45_70_4_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 64 8 ]
        [ "D_Melpa_Madre_source_WatchFaceSettings_mc_63_45_70_4_start" "D_Melpa_Madre_source_WatchFaceSettings_mc_63_45_70_4_stop" ]
        %showSeconds.1 = local;
        symbol [ showSeconds %showSeconds.1 64 12 23 ];
        symbol [ Storage %tmp.1 64 26 33 ];
        %tmp.1 = getm $.Toybox.Application.Storage;
        symbol [ getValue %tmp.2 64 34 42 ];
        %tmp.2 = getv function %tmp.1 :getValue;
        %tmp.3 = "showSeconds";
        %tmp.4 = invoke %tmp.1 %tmp.2(%tmp.3);
        lputv %showSeconds.1 %tmp.4;
        symbol [ showSeconds %showSeconds.1 64 12 23 ];
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 65 8 ]
D_Melpa_Madre_source_WatchFaceSettings_mc_65_8_67_8_if_stmt:
        %tmp.5 = lgetv %showSeconds.1;
        symbol [ showSeconds %tmp.5 65 12 23 ];
        %tmp.6 = null;
        %tmp.7 = eq %tmp.5 %tmp.6;
        bf %tmp.7 @D_Melpa_Madre_source_WatchFaceSettings_mc_65_8_67_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_65_8_67_8_if_true:
D_Melpa_Madre_source_WatchFaceSettings_mc_65_33_67_8_start:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 66 12 ]
        %tmp.8 = false;
        lputv %showSeconds.1 %tmp.8;
        symbol [ showSeconds %showSeconds.1 66 12 23 ];
D_Melpa_Madre_source_WatchFaceSettings_mc_65_33_67_8_stop:
        goto @D_Melpa_Madre_source_WatchFaceSettings_mc_65_8_67_8_if_end;
D_Melpa_Madre_source_WatchFaceSettings_mc_65_8_67_8_if_end:
[ "D:\Melpa\Madre\source\WatchFaceSettings.mc" 69 8 ]
        symbol [ Storage %tmp.9 69 8 15 ];
        %tmp.9 = getm $.Toybox.Application.Storage;
        symbol [ setValue %tmp.10 69 16 24 ];
        %tmp.10 = getv function %tmp.9 :setValue;
        %tmp.11 = "showSeconds";
        %tmp.12 = lgetv %showSeconds.1;
        symbol [ showSeconds %tmp.12 69 41 52 ];
        %tmp.13 = not %tmp.12;
        invoke %tmp.9 %tmp.10(%tmp.11, %tmp.13);
D_Melpa_Madre_source_WatchFaceSettings_mc_63_45_70_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 72; @symbol_functiondef = [customizeDataFields,72,21,40]; ]
    private
    function customizeDataFields() as Void {
    }
}
[ @file = "D:\Melpa\Madre\source\WatchFaceSettings.mc"; @line = 1; ]
<init> {
}
