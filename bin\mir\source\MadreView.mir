[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 1; @symbol_importdef<0> = [Toybox,1,7,13]; @symbol_importdef<1> = [Graphics,1,14,22]; ]
import Toybox.Graphics;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 2; @symbol_importdef<0> = [Toybox,2,7,13]; @symbol_importdef<1> = [Lang,2,14,18]; ]
import Toybox.Lang;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 3; @symbol_importdef<0> = [Toybox,3,7,13]; @symbol_importdef<1> = [System,3,14,20]; ]
import Toybox.System;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 4; @symbol_importdef<0> = [Toybox,4,7,13]; @symbol_importdef<1> = [WatchUi,4,14,21]; ]
import Toybox.WatchUi;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 5; @symbol_importdef<0> = [Toybox,5,7,13]; @symbol_importdef<1> = [ActivityMonitor,5,14,29]; ]
import Toybox.ActivityMonitor;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 6; @symbol_importdef<0> = [Toybox,6,7,13]; @symbol_importdef<1> = [Time,6,14,18]; ]
import Toybox.Time;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 7; @symbol_importdef<0> = [Toybox,7,7,13]; @symbol_importdef<1> = [Time,7,14,18]; @symbol_importdef<2> = [Gregorian,7,19,28]; ]
import Toybox.Time.Gregorian;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 8; @symbol_importdef<0> = [Toybox,8,7,13]; @symbol_importdef<1> = [UserProfile,8,14,25]; ]
import Toybox.UserProfile;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 9; @symbol_importdef<0> = [Toybox,9,7,13]; @symbol_importdef<1> = [Math,9,14,18]; ]
import Toybox.Math;
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 11; @symbol_classdef = [MadreView,11,6,15]; @symbol_extends<0> = [WatchUi,11,24,31]; @symbol_extends<1> = [WatchFace,11,32,41]; ]
class MadreView extends WatchUi.WatchFace {
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 11; ]
    <init> {
[ "D:\Melpa\Madre\source\MadreView.mc" 12 16 ]
        %tmp.1 = newa 2;
        %tmp.2 = 0;
        %tmp.3 = dup %tmp.1;
        %tmp.4 = aputv %tmp.3 0 %tmp.2;
        %tmp.5 = 0;
        %tmp.6 = dup %tmp.4;
        %tmp.7 = aputv %tmp.6 1 %tmp.5;
        putv self :_screenCenterPoint %tmp.7;
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 11; ]
    static
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 12; @position = 16; @symbol_vardef = [_screenCenterPoint,12,16,34]; @symbol_type<0> = [Array,12,38,43]; @symbol_type<1> = [Number,12,44,50]; ]
    private
    var _screenCenterPoint as Array<Number>;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 13; @position = 16; @symbol_vardef = [_screenWidth,13,16,28]; @symbol_type<0> = [Number,13,32,38]; ]
    private
    var _screenWidth as Number = 0;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 14; @position = 16; @symbol_vardef = [_screenHeight,14,16,29]; @symbol_type<0> = [Number,14,33,39]; ]
    private
    var _screenHeight as Number = 0;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 15; @position = 16; @symbol_vardef = [_partialUpdatesAllowed,15,16,38]; @symbol_type<0> = [Boolean,15,42,49]; ]
    private
    var _partialUpdatesAllowed as Boolean;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 16; @position = 16; @symbol_vardef = [_isAwake,16,16,24]; @symbol_type<0> = [Boolean,16,28,35]; ]
    private
    var _isAwake as Boolean = false;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 19; @position = 16; @symbol_vardef = [_backgroundColor,19,16,32]; @symbol_type<0> = [Number,19,36,42]; ]
    private
    var _backgroundColor as Number = 0;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 20; @position = 16; @symbol_vardef = [_foregroundColor,20,16,32]; @symbol_type<0> = [Number,20,36,42]; ]
    private
    var _foregroundColor as Number = 0;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 21; @position = 16; @symbol_vardef = [_accentColor,21,16,28]; @symbol_type<0> = [Number,21,32,38]; ]
    private
    var _accentColor as Number = 0;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 24; @position = 16; @symbol_vardef = [_timeFont,24,16,25]; @symbol_type<0> = [Graphics,24,29,37]; @symbol_type<1> = [VectorFont,24,38,48]; ]
    private
    var _timeFont as Graphics.VectorFont or Null;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 25; @position = 16; @symbol_vardef = [_dataFont,25,16,25]; @symbol_type<0> = [Graphics,25,29,37]; @symbol_type<1> = [VectorFont,25,38,48]; ]
    private
    var _dataFont as Graphics.VectorFont or Null;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 26; @position = 16; @symbol_vardef = [_smallFont,26,16,26]; @symbol_type<0> = [Graphics,26,30,38]; @symbol_type<1> = [VectorFont,26,39,49]; ]
    private
    var _smallFont as Graphics.VectorFont or Null;
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 30; @symbol_functiondef = [initialize,30,13,23]; ]
    function initialize() as Void {
D_Melpa_Madre_source_MadreView_mc_30_26_34_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 31 8 ]
        symbol [ WatchFace %tmp.2 31 8 17 ];
        %tmp.2 = getv ? :WatchFace;
        symbol [ initialize %tmp.3 31 18 28 ];
        %tmp.3 = getv function %tmp.2 :initialize;
        invoke %tmp.2 %tmp.3();
[ "D:\Melpa\Madre\source\MadreView.mc" 32 9 ]
        symbol [ WatchUi %tmp.4 32 35 42 ];
        %tmp.4 = getm $.Toybox.WatchUi;
        symbol [ WatchFace %tmp.5 32 43 52 ];
        %tmp.5 = getv %tmp.4 :WatchFace;
        %tmp.7 = const :onPartialUpdate;
        symbol [ onPartialUpdate %tmp.7 32 58 73 const ];
        %tmp.8 = canhazplz %tmp.5 %tmp.7;
        symbol [ _partialUpdatesAllowed ? 32 9 31 ];
        putv self :_partialUpdatesAllowed %tmp.8;
[ "D:\Melpa\Madre\source\MadreView.mc" 33 8 ]
        %tmp.9 = true;
        symbol [ _isAwake ? 33 8 16 ];
        putv self :_isAwake %tmp.9;
D_Melpa_Madre_source_MadreView_mc_30_26_34_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 37; @symbol_functiondef = [onLayout,37,13,21]; @symbol_param<0> = [dc,37,22,24]; @symbol_param<0>_type<0> = [Dc,37,28,30]; ]
    function onLayout(dc as Dc) as Void {
D_Melpa_Madre_source_MadreView_mc_37_40_58_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 38 8 ]
        %tmp.1 = lgetv %dc;
        symbol [ dc %tmp.1 38 23 25 ];
        symbol [ getWidth %tmp.2 38 26 34 ];
        %tmp.2 = getv function %tmp.1 :getWidth;
        %tmp.3 = invoke %tmp.1 %tmp.2();
        symbol [ _screenWidth ? 38 8 20 ];
        putv self :_screenWidth %tmp.3;
[ "D:\Melpa\Madre\source\MadreView.mc" 39 8 ]
        %tmp.4 = lgetv %dc;
        symbol [ dc %tmp.4 39 24 26 ];
        symbol [ getHeight %tmp.5 39 27 36 ];
        %tmp.5 = getv function %tmp.4 :getHeight;
        %tmp.6 = invoke %tmp.4 %tmp.5();
        symbol [ _screenHeight ? 39 8 21 ];
        putv self :_screenHeight %tmp.6;
[ "D:\Melpa\Madre\source\MadreView.mc" 40 8 ]
        %tmp.7 = newa 2;
        symbol [ _screenWidth %tmp.9 40 30 42 ];
        %tmp.9 = getv ? :_screenWidth;
        %tmp.10 = 2;
        %tmp.11 = div %tmp.9 %tmp.10;
        %tmp.12 = dup %tmp.7;
        %tmp.13 = aputv %tmp.12 0 %tmp.11;
        symbol [ _screenHeight %tmp.15 40 48 61 ];
        %tmp.15 = getv ? :_screenHeight;
        %tmp.16 = 2;
        %tmp.17 = div %tmp.15 %tmp.16;
        %tmp.18 = dup %tmp.13;
        %tmp.19 = aputv %tmp.18 1 %tmp.17;
        symbol [ _screenCenterPoint ? 40 8 26 ];
        putv self :_screenCenterPoint %tmp.19;
[ "D:\Melpa\Madre\source\MadreView.mc" 43 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_37_40_58_4_start" "D_Melpa_Madre_source_MadreView_mc_37_40_58_4_stop" ]
        %deviceSettings.1 = local;
        symbol [ deviceSettings %deviceSettings.1 43 12 26 ];
        symbol [ System %tmp.20 43 29 35 ];
        %tmp.20 = getm $.Toybox.System;
        symbol [ getDeviceSettings %tmp.21 43 36 53 ];
        %tmp.21 = getv function %tmp.20 :getDeviceSettings;
        %tmp.22 = invoke %tmp.20 %tmp.21();
        lputv %deviceSettings.1 %tmp.22;
        symbol [ deviceSettings %deviceSettings.1 43 12 26 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 44 8 ]
D_Melpa_Madre_source_MadreView_mc_44_8_52_8_if_stmt:
        %tmp.23 = lgetv %deviceSettings.1;
        symbol [ deviceSettings %tmp.23 44 12 26 ];
        symbol [ requiresBurnInProtection %tmp.24 44 27 51 ];
        %tmp.24 = getv %tmp.23 :requiresBurnInProtection;
        bf %tmp.24 @D_Melpa_Madre_source_MadreView_mc_44_8_52_8_if_else_false;
D_Melpa_Madre_source_MadreView_mc_44_8_52_8_if_true:
D_Melpa_Madre_source_MadreView_mc_44_53_48_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 45 12 ]
        symbol [ Graphics %tmp.25 45 31 39 ];
        %tmp.25 = getm $.Toybox.Graphics;
        symbol [ COLOR_BLACK %tmp.26 45 40 51 ];
        %tmp.26 = getv %tmp.25 :COLOR_BLACK;
        symbol [ _backgroundColor ? 45 12 28 ];
        putv self :_backgroundColor %tmp.26;
[ "D:\Melpa\Madre\source\MadreView.mc" 46 12 ]
        symbol [ Graphics %tmp.27 46 31 39 ];
        %tmp.27 = getm $.Toybox.Graphics;
        symbol [ COLOR_WHITE %tmp.28 46 40 51 ];
        %tmp.28 = getv %tmp.27 :COLOR_WHITE;
        symbol [ _foregroundColor ? 46 12 28 ];
        putv self :_foregroundColor %tmp.28;
[ "D:\Melpa\Madre\source\MadreView.mc" 47 12 ]
        symbol [ Graphics %tmp.29 47 27 35 ];
        %tmp.29 = getm $.Toybox.Graphics;
        symbol [ COLOR_BLUE %tmp.30 47 36 46 ];
        %tmp.30 = getv %tmp.29 :COLOR_BLUE;
        symbol [ _accentColor ? 47 12 24 ];
        putv self :_accentColor %tmp.30;
D_Melpa_Madre_source_MadreView_mc_44_53_48_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_44_8_52_8_if_end;
D_Melpa_Madre_source_MadreView_mc_44_8_52_8_if_else_false:
D_Melpa_Madre_source_MadreView_mc_48_15_52_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 49 12 ]
        symbol [ Graphics %tmp.31 49 31 39 ];
        %tmp.31 = getm $.Toybox.Graphics;
        symbol [ COLOR_BLACK %tmp.32 49 40 51 ];
        %tmp.32 = getv %tmp.31 :COLOR_BLACK;
        symbol [ _backgroundColor ? 49 12 28 ];
        putv self :_backgroundColor %tmp.32;
[ "D:\Melpa\Madre\source\MadreView.mc" 50 12 ]
        symbol [ Graphics %tmp.33 50 31 39 ];
        %tmp.33 = getm $.Toybox.Graphics;
        symbol [ COLOR_WHITE %tmp.34 50 40 51 ];
        %tmp.34 = getv %tmp.33 :COLOR_WHITE;
        symbol [ _foregroundColor ? 50 12 28 ];
        putv self :_foregroundColor %tmp.34;
[ "D:\Melpa\Madre\source\MadreView.mc" 51 12 ]
        symbol [ Graphics %tmp.35 51 27 35 ];
        %tmp.35 = getm $.Toybox.Graphics;
        symbol [ COLOR_BLUE %tmp.36 51 36 46 ];
        %tmp.36 = getv %tmp.35 :COLOR_BLUE;
        symbol [ _accentColor ? 51 12 24 ];
        putv self :_accentColor %tmp.36;
D_Melpa_Madre_source_MadreView_mc_48_15_52_8_stop:
D_Melpa_Madre_source_MadreView_mc_44_8_52_8_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 55 8 ]
        symbol [ Graphics %tmp.37 55 20 28 ];
        %tmp.37 = getm $.Toybox.Graphics;
        symbol [ getVectorFont %tmp.38 55 29 42 ];
        %tmp.38 = getv function %tmp.37 :getVectorFont;
        %tmp.39 = newd 2;
        %tmp.41 = const :face;
        symbol [ face %tmp.41 55 45 49 const ];
        %tmp.42 = "RobotoCondensed";
        %tmp.43 = dup %tmp.39;
        %tmp.44 = aputv %tmp.43 %tmp.41 %tmp.42;
        %tmp.46 = const :size;
        symbol [ size %tmp.46 55 71 75 const ];
        %tmp.47 = 72;
        %tmp.48 = dup %tmp.44;
        %tmp.49 = aputv %tmp.48 %tmp.46 %tmp.47;
        %tmp.50 = invoke %tmp.37 %tmp.38(%tmp.49);
        symbol [ _timeFont ? 55 8 17 ];
        putv self :_timeFont %tmp.50;
[ "D:\Melpa\Madre\source\MadreView.mc" 56 8 ]
        symbol [ Graphics %tmp.51 56 20 28 ];
        %tmp.51 = getm $.Toybox.Graphics;
        symbol [ getVectorFont %tmp.52 56 29 42 ];
        %tmp.52 = getv function %tmp.51 :getVectorFont;
        %tmp.53 = newd 2;
        %tmp.55 = const :face;
        symbol [ face %tmp.55 56 45 49 const ];
        %tmp.56 = "RobotoCondensed";
        %tmp.57 = dup %tmp.53;
        %tmp.58 = aputv %tmp.57 %tmp.55 %tmp.56;
        %tmp.60 = const :size;
        symbol [ size %tmp.60 56 71 75 const ];
        %tmp.61 = 24;
        %tmp.62 = dup %tmp.58;
        %tmp.63 = aputv %tmp.62 %tmp.60 %tmp.61;
        %tmp.64 = invoke %tmp.51 %tmp.52(%tmp.63);
        symbol [ _dataFont ? 56 8 17 ];
        putv self :_dataFont %tmp.64;
[ "D:\Melpa\Madre\source\MadreView.mc" 57 8 ]
        symbol [ Graphics %tmp.65 57 21 29 ];
        %tmp.65 = getm $.Toybox.Graphics;
        symbol [ getVectorFont %tmp.66 57 30 43 ];
        %tmp.66 = getv function %tmp.65 :getVectorFont;
        %tmp.67 = newd 2;
        %tmp.69 = const :face;
        symbol [ face %tmp.69 57 46 50 const ];
        %tmp.70 = "RobotoCondensed";
        %tmp.71 = dup %tmp.67;
        %tmp.72 = aputv %tmp.71 %tmp.69 %tmp.70;
        %tmp.74 = const :size;
        symbol [ size %tmp.74 57 72 76 const ];
        %tmp.75 = 18;
        %tmp.76 = dup %tmp.72;
        %tmp.77 = aputv %tmp.76 %tmp.74 %tmp.75;
        %tmp.78 = invoke %tmp.65 %tmp.66(%tmp.77);
        symbol [ _smallFont ? 57 8 18 ];
        putv self :_smallFont %tmp.78;
D_Melpa_Madre_source_MadreView_mc_37_40_58_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 63; @symbol_functiondef = [onShow,63,13,19]; ]
    function onShow() as Void {
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 67; @symbol_functiondef = [onUpdate,67,13,21]; @symbol_param<0> = [dc,67,22,24]; @symbol_param<0>_type<0> = [Dc,67,28,30]; ]
    function onUpdate(dc as Dc) as Void {
D_Melpa_Madre_source_MadreView_mc_67_40_107_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 68 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_67_40_107_4_start" "D_Melpa_Madre_source_MadreView_mc_67_40_107_4_stop" ]
        %targetDc.1 = local;
        symbol [ targetDc %targetDc.1 68 12 20 ];
        %tmp.1 = null;
        lputv %targetDc.1 %tmp.1;
        symbol [ targetDc %targetDc.1 68 12 20 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 70 8 ]
D_Melpa_Madre_source_MadreView_mc_70_8_74_8_if_stmt:
        symbol [ _partialUpdatesAllowed %tmp.3 70 12 34 ];
        %tmp.3 = getv ? :_partialUpdatesAllowed;
        bf %tmp.3 @D_Melpa_Madre_source_MadreView_mc_70_8_74_8_if_else_false;
D_Melpa_Madre_source_MadreView_mc_70_8_74_8_if_true:
D_Melpa_Madre_source_MadreView_mc_70_36_72_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 71 12 ]
        %tmp.4 = lgetv %dc;
        symbol [ dc %tmp.4 71 23 25 ];
        lputv %targetDc.1 %tmp.4;
        symbol [ targetDc %targetDc.1 71 12 20 ];
D_Melpa_Madre_source_MadreView_mc_70_36_72_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_70_8_74_8_if_end;
D_Melpa_Madre_source_MadreView_mc_70_8_74_8_if_else_false:
D_Melpa_Madre_source_MadreView_mc_72_15_74_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 73 12 ]
        %tmp.5 = lgetv %dc;
        symbol [ dc %tmp.5 73 23 25 ];
        lputv %targetDc.1 %tmp.5;
        symbol [ targetDc %targetDc.1 73 12 20 ];
D_Melpa_Madre_source_MadreView_mc_72_15_74_8_stop:
D_Melpa_Madre_source_MadreView_mc_70_8_74_8_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 76 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_67_40_107_4_start" "D_Melpa_Madre_source_MadreView_mc_67_40_107_4_stop" ]
        %clockTime.2 = local;
        symbol [ clockTime %clockTime.2 76 12 21 ];
        symbol [ System %tmp.6 76 24 30 ];
        %tmp.6 = getm $.Toybox.System;
        symbol [ getClockTime %tmp.7 76 31 43 ];
        %tmp.7 = getv function %tmp.6 :getClockTime;
        %tmp.8 = invoke %tmp.6 %tmp.7();
        lputv %clockTime.2 %tmp.8;
        symbol [ clockTime %clockTime.2 76 12 21 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 77 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_67_40_107_4_start" "D_Melpa_Madre_source_MadreView_mc_67_40_107_4_stop" ]
        %date.3 = local;
        symbol [ date %date.3 77 12 16 ];
        symbol [ Gregorian %tmp.9 77 19 28 ];
        %tmp.9 = getm $.Toybox.Time.Gregorian;
        symbol [ info %tmp.10 77 29 33 ];
        %tmp.10 = getv function %tmp.9 :info;
        symbol [ Time %tmp.11 77 34 38 ];
        %tmp.11 = getm $.Toybox.Time;
        symbol [ now %tmp.12 77 39 42 ];
        %tmp.12 = getv function %tmp.11 :now;
        %tmp.13 = invoke %tmp.11 %tmp.12();
        symbol [ Time %tmp.14 77 46 50 ];
        %tmp.14 = getm $.Toybox.Time;
        symbol [ FORMAT_MEDIUM %tmp.15 77 51 64 ];
        %tmp.15 = getv %tmp.14 :FORMAT_MEDIUM;
        %tmp.16 = invoke %tmp.9 %tmp.10(%tmp.13, %tmp.15);
        lputv %date.3 %tmp.16;
        symbol [ date %date.3 77 12 16 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 80 8 ]
        %tmp.17 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.17 80 8 16 ];
        symbol [ setColor %tmp.18 80 17 25 ];
        %tmp.18 = getv function %tmp.17 :setColor;
        symbol [ _backgroundColor %tmp.20 80 26 42 ];
        %tmp.20 = getv ? :_backgroundColor;
        symbol [ _backgroundColor %tmp.22 80 44 60 ];
        %tmp.22 = getv ? :_backgroundColor;
        invoke %tmp.17 %tmp.18(%tmp.20, %tmp.22);
[ "D:\Melpa\Madre\source\MadreView.mc" 81 8 ]
        %tmp.23 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.23 81 8 16 ];
        symbol [ clear %tmp.24 81 17 22 ];
        %tmp.24 = getv function %tmp.23 :clear;
        invoke %tmp.23 %tmp.24();
[ "D:\Melpa\Madre\source\MadreView.mc" 84 8 ]
        %tmp.25 = self;
        symbol [ drawTime %tmp.26 84 8 16 ];
        %tmp.26 = getv function %tmp.25 :drawTime;
        %tmp.27 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.27 84 17 25 ];
        %tmp.28 = lgetv %clockTime.2;
        symbol [ clockTime %tmp.28 84 27 36 ];
        invoke %tmp.25 %tmp.26(%tmp.27, %tmp.28);
[ "D:\Melpa\Madre\source\MadreView.mc" 87 8 ]
        %tmp.29 = self;
        symbol [ drawDate %tmp.30 87 8 16 ];
        %tmp.30 = getv function %tmp.29 :drawDate;
        %tmp.31 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.31 87 17 25 ];
        %tmp.32 = lgetv %date.3;
        symbol [ date %tmp.32 87 27 31 ];
        invoke %tmp.29 %tmp.30(%tmp.31, %tmp.32);
[ "D:\Melpa\Madre\source\MadreView.mc" 90 8 ]
D_Melpa_Madre_source_MadreView_mc_90_8_102_8_if_stmt:
        symbol [ _isAwake %tmp.34 90 12 20 ];
        %tmp.34 = getv ? :_isAwake;
        bf %tmp.34 @D_Melpa_Madre_source_MadreView_mc_90_8_102_8_if_end;
D_Melpa_Madre_source_MadreView_mc_90_8_102_8_if_true:
D_Melpa_Madre_source_MadreView_mc_90_22_102_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 92 12 ]
        %tmp.35 = self;
        symbol [ drawActivityData %tmp.36 92 12 28 ];
        %tmp.36 = getv function %tmp.35 :drawActivityData;
        %tmp.37 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.37 92 29 37 ];
        invoke %tmp.35 %tmp.36(%tmp.37);
[ "D:\Melpa\Madre\source\MadreView.mc" 95 12 ]
        %tmp.38 = self;
        symbol [ drawBattery %tmp.39 95 12 23 ];
        %tmp.39 = getv function %tmp.38 :drawBattery;
        %tmp.40 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.40 95 24 32 ];
        invoke %tmp.38 %tmp.39(%tmp.40);
[ "D:\Melpa\Madre\source\MadreView.mc" 98 12 ]
        %tmp.41 = self;
        symbol [ drawHeartRate %tmp.42 98 12 25 ];
        %tmp.42 = getv function %tmp.41 :drawHeartRate;
        %tmp.43 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.43 98 26 34 ];
        invoke %tmp.41 %tmp.42(%tmp.43);
[ "D:\Melpa\Madre\source\MadreView.mc" 101 12 ]
        %tmp.44 = self;
        symbol [ drawSteps %tmp.45 101 12 21 ];
        %tmp.45 = getv function %tmp.44 :drawSteps;
        %tmp.46 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.46 101 22 30 ];
        invoke %tmp.44 %tmp.45(%tmp.46);
D_Melpa_Madre_source_MadreView_mc_90_22_102_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_90_8_102_8_if_end;
D_Melpa_Madre_source_MadreView_mc_90_8_102_8_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 104 8 ]
D_Melpa_Madre_source_MadreView_mc_104_8_106_8_if_stmt:
        symbol [ _partialUpdatesAllowed %tmp.48 104 12 34 ];
        %tmp.48 = getv ? :_partialUpdatesAllowed;
        bf %tmp.48 @D_Melpa_Madre_source_MadreView_mc_104_8_106_8_if_end;
D_Melpa_Madre_source_MadreView_mc_104_8_106_8_if_true:
D_Melpa_Madre_source_MadreView_mc_104_36_106_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 105 12 ]
        %tmp.49 = self;
        symbol [ onPartialUpdate %tmp.50 105 12 27 ];
        %tmp.50 = getv function %tmp.49 :onPartialUpdate;
        %tmp.51 = lgetv %targetDc.1;
        symbol [ targetDc %tmp.51 105 28 36 ];
        invoke %tmp.49 %tmp.50(%tmp.51);
D_Melpa_Madre_source_MadreView_mc_104_36_106_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_104_8_106_8_if_end;
D_Melpa_Madre_source_MadreView_mc_104_8_106_8_if_end:
D_Melpa_Madre_source_MadreView_mc_67_40_107_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 112; @symbol_functiondef = [onHide,112,13,19]; ]
    function onHide() as Void {
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 116; @symbol_functiondef = [onExitSleep,116,13,24]; ]
    function onExitSleep() as Void {
D_Melpa_Madre_source_MadreView_mc_116_35_119_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 117 8 ]
        %tmp.1 = true;
        symbol [ _isAwake ? 117 8 16 ];
        putv self :_isAwake %tmp.1;
[ "D:\Melpa\Madre\source\MadreView.mc" 118 8 ]
        symbol [ WatchUi %tmp.2 118 8 15 ];
        %tmp.2 = getm $.Toybox.WatchUi;
        symbol [ requestUpdate %tmp.3 118 16 29 ];
        %tmp.3 = getv function %tmp.2 :requestUpdate;
        invoke %tmp.2 %tmp.3();
D_Melpa_Madre_source_MadreView_mc_116_35_119_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 122; @symbol_functiondef = [onEnterSleep,122,13,25]; ]
    function onEnterSleep() as Void {
D_Melpa_Madre_source_MadreView_mc_122_36_125_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 123 9 ]
        %tmp.1 = false;
        symbol [ _isAwake ? 123 9 17 ];
        putv self :_isAwake %tmp.1;
[ "D:\Melpa\Madre\source\MadreView.mc" 124 8 ]
        symbol [ WatchUi %tmp.2 124 8 15 ];
        %tmp.2 = getm $.Toybox.WatchUi;
        symbol [ requestUpdate %tmp.3 124 16 29 ];
        %tmp.3 = getv function %tmp.2 :requestUpdate;
        invoke %tmp.2 %tmp.3();
D_Melpa_Madre_source_MadreView_mc_122_36_125_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 127; @symbol_functiondef = [drawTime,127,21,29]; @symbol_param<0> = [dc,127,30,32]; @symbol_param<0>_type<0> = [Graphics,127,36,44]; @symbol_param<0>_type<1> = [Dc,127,45,47]; @symbol_param<1> = [clockTime,127,49,58]; @symbol_param<1>_type<0> = [System,127,62,68]; @symbol_param<1>_type<1> = [ClockTime,127,69,78]; ]
    private
    function drawTime(dc as Graphics.Dc, clockTime as System.ClockTime) as Void {
D_Melpa_Madre_source_MadreView_mc_127_88_155_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 128 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_start" "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_stop" ]
        %timeString.1 = local;
        symbol [ timeString %timeString.1 128 12 22 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 129 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_start" "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_stop" ]
        %is24Hour.2 = local;
        symbol [ is24Hour %is24Hour.2 129 12 20 ];
        symbol [ System %tmp.1 129 23 29 ];
        %tmp.1 = getm $.Toybox.System;
        symbol [ getDeviceSettings %tmp.2 129 30 47 ];
        %tmp.2 = getv function %tmp.1 :getDeviceSettings;
        %tmp.3 = invoke %tmp.1 %tmp.2();
        symbol [ is24Hour %tmp.4 129 50 58 ];
        %tmp.4 = getv %tmp.3 :is24Hour;
        lputv %is24Hour.2 %tmp.4;
        symbol [ is24Hour %is24Hour.2 129 12 20 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 131 8 ]
D_Melpa_Madre_source_MadreView_mc_131_8_147_8_if_stmt:
        %tmp.5 = lgetv %is24Hour.2;
        symbol [ is24Hour %tmp.5 131 12 20 ];
        bf %tmp.5 @D_Melpa_Madre_source_MadreView_mc_131_8_147_8_if_else_false;
D_Melpa_Madre_source_MadreView_mc_131_8_147_8_if_true:
D_Melpa_Madre_source_MadreView_mc_131_22_136_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 132 12 ]
        symbol [ Lang %tmp.6 132 25 29 ];
        %tmp.6 = getm $.Toybox.Lang;
        symbol [ format %tmp.7 132 30 36 ];
        %tmp.7 = getv function %tmp.6 :format;
        %tmp.8 = "$1$:$2$";
        %tmp.9 = newa 2;
        %tmp.10 = lgetv %clockTime;
        symbol [ clockTime %tmp.10 133 16 25 ];
        symbol [ hour %tmp.11 133 26 30 ];
        %tmp.11 = getv %tmp.10 :hour;
        symbol [ format %tmp.12 133 31 37 ];
        %tmp.12 = getv function %tmp.11 :format;
        %tmp.13 = "%02d";
        %tmp.14 = invoke %tmp.11 %tmp.12(%tmp.13);
        %tmp.15 = dup %tmp.9;
        %tmp.16 = aputv %tmp.15 0 %tmp.14;
        %tmp.17 = lgetv %clockTime;
        symbol [ clockTime %tmp.17 134 16 25 ];
        symbol [ min %tmp.18 134 26 29 ];
        %tmp.18 = getv %tmp.17 :min;
        symbol [ format %tmp.19 134 30 36 ];
        %tmp.19 = getv function %tmp.18 :format;
        %tmp.20 = "%02d";
        %tmp.21 = invoke %tmp.18 %tmp.19(%tmp.20);
        %tmp.22 = dup %tmp.16;
        %tmp.23 = aputv %tmp.22 1 %tmp.21;
        %tmp.24 = invoke %tmp.6 %tmp.7(%tmp.8, %tmp.23);
        lputv %timeString.1 %tmp.24;
        symbol [ timeString %timeString.1 132 12 22 ];
D_Melpa_Madre_source_MadreView_mc_131_22_136_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_131_8_147_8_if_end;
D_Melpa_Madre_source_MadreView_mc_131_8_147_8_if_else_false:
D_Melpa_Madre_source_MadreView_mc_136_15_147_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 137 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_136_15_147_8_start" "D_Melpa_Madre_source_MadreView_mc_136_15_147_8_stop" ]
        %hour.3 = local;
        symbol [ hour %hour.3 137 16 20 ];
        %tmp.25 = lgetv %clockTime;
        symbol [ clockTime %tmp.25 137 23 32 ];
        symbol [ hour %tmp.26 137 33 37 ];
        %tmp.26 = getv %tmp.25 :hour;
        lputv %hour.3 %tmp.26;
        symbol [ hour %hour.3 137 16 20 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 138 12 ]
D_Melpa_Madre_source_MadreView_mc_138_12_142_12_if_stmt:
        %tmp.27 = lgetv %hour.3;
        symbol [ hour %tmp.27 138 16 20 ];
        %tmp.28 = 12;
        %tmp.29 = gt %tmp.27 %tmp.28;
        bf %tmp.29 @D_Melpa_Madre_source_MadreView_mc_138_12_142_12_if_false;
D_Melpa_Madre_source_MadreView_mc_138_12_142_12_if_true:
D_Melpa_Madre_source_MadreView_mc_138_27_140_12_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 139 16 ]
        %tmp.30 = lgetv %hour.3;
        symbol [ hour %tmp.30 139 23 27 ];
        %tmp.31 = 12;
        %tmp.32 = sub %tmp.30 %tmp.31;
        lputv %hour.3 %tmp.32;
        symbol [ hour %hour.3 139 16 20 ];
D_Melpa_Madre_source_MadreView_mc_138_27_140_12_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_138_12_142_12_if_end;
D_Melpa_Madre_source_MadreView_mc_138_12_142_12_if_false:
[ "D:\Melpa\Madre\source\MadreView.mc" 140 19 ]
D_Melpa_Madre_source_MadreView_mc_140_19_142_12_if_stmt:
        %tmp.33 = lgetv %hour.3;
        symbol [ hour %tmp.33 140 23 27 ];
        %tmp.34 = 0;
        %tmp.35 = eq %tmp.33 %tmp.34;
        bf %tmp.35 @D_Melpa_Madre_source_MadreView_mc_140_19_142_12_if_end;
D_Melpa_Madre_source_MadreView_mc_140_19_142_12_if_true:
D_Melpa_Madre_source_MadreView_mc_140_34_142_12_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 141 16 ]
        %tmp.36 = 12;
        lputv %hour.3 %tmp.36;
        symbol [ hour %hour.3 141 16 20 ];
D_Melpa_Madre_source_MadreView_mc_140_34_142_12_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_140_19_142_12_if_end;
D_Melpa_Madre_source_MadreView_mc_140_19_142_12_if_end:
D_Melpa_Madre_source_MadreView_mc_138_12_142_12_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 143 12 ]
        symbol [ Lang %tmp.37 143 25 29 ];
        %tmp.37 = getm $.Toybox.Lang;
        symbol [ format %tmp.38 143 30 36 ];
        %tmp.38 = getv function %tmp.37 :format;
        %tmp.39 = "$1$:$2$";
        %tmp.40 = newa 2;
        %tmp.41 = lgetv %hour.3;
        symbol [ hour %tmp.41 144 16 20 ];
        symbol [ format %tmp.42 144 21 27 ];
        %tmp.42 = getv function %tmp.41 :format;
        %tmp.43 = "%d";
        %tmp.44 = invoke %tmp.41 %tmp.42(%tmp.43);
        %tmp.45 = dup %tmp.40;
        %tmp.46 = aputv %tmp.45 0 %tmp.44;
        %tmp.47 = lgetv %clockTime;
        symbol [ clockTime %tmp.47 145 16 25 ];
        symbol [ min %tmp.48 145 26 29 ];
        %tmp.48 = getv %tmp.47 :min;
        symbol [ format %tmp.49 145 30 36 ];
        %tmp.49 = getv function %tmp.48 :format;
        %tmp.50 = "%02d";
        %tmp.51 = invoke %tmp.48 %tmp.49(%tmp.50);
        %tmp.52 = dup %tmp.46;
        %tmp.53 = aputv %tmp.52 1 %tmp.51;
        %tmp.54 = invoke %tmp.37 %tmp.38(%tmp.39, %tmp.53);
        lputv %timeString.1 %tmp.54;
        symbol [ timeString %timeString.1 143 12 22 ];
D_Melpa_Madre_source_MadreView_mc_136_15_147_8_stop:
D_Melpa_Madre_source_MadreView_mc_131_8_147_8_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 149 8 ]
        %tmp.55 = lgetv %dc;
        symbol [ dc %tmp.55 149 8 10 ];
        symbol [ setColor %tmp.56 149 11 19 ];
        %tmp.56 = getv function %tmp.55 :setColor;
        symbol [ _foregroundColor %tmp.58 149 20 36 ];
        %tmp.58 = getv ? :_foregroundColor;
        symbol [ Graphics %tmp.59 149 38 46 ];
        %tmp.59 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.60 149 47 64 ];
        %tmp.60 = getv %tmp.59 :COLOR_TRANSPARENT;
        invoke %tmp.55 %tmp.56(%tmp.58, %tmp.60);
[ "D:\Melpa\Madre\source\MadreView.mc" 150 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_start" "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_stop" ]
        %timeTextDimensions.4 = local;
        symbol [ timeTextDimensions %timeTextDimensions.4 150 12 30 ];
        %tmp.61 = lgetv %dc;
        symbol [ dc %tmp.61 150 33 35 ];
        symbol [ getTextDimensions %tmp.62 150 36 53 ];
        %tmp.62 = getv function %tmp.61 :getTextDimensions;
        %tmp.63 = lgetv %timeString.1;
        symbol [ timeString %tmp.63 150 54 64 ];
        symbol [ _timeFont %tmp.65 150 66 75 ];
        %tmp.65 = getv ? :_timeFont;
        %tmp.66 = invoke %tmp.61 %tmp.62(%tmp.63, %tmp.65);
        lputv %timeTextDimensions.4 %tmp.66;
        symbol [ timeTextDimensions %timeTextDimensions.4 150 12 30 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 151 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_start" "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_stop" ]
        %timeX.5 = local;
        symbol [ timeX %timeX.5 151 12 17 ];
        symbol [ _screenWidth %tmp.68 151 21 33 ];
        %tmp.68 = getv ? :_screenWidth;
        %tmp.69 = lgetv %timeTextDimensions.4;
        symbol [ timeTextDimensions %tmp.69 151 36 54 ];
        %tmp.70 = 0;
        %tmp.71 = agetv %tmp.69 %tmp.70;
        %tmp.72 = sub %tmp.68 %tmp.71;
        %tmp.73 = 2;
        %tmp.74 = div %tmp.72 %tmp.73;
        lputv %timeX.5 %tmp.74;
        symbol [ timeX %timeX.5 151 12 17 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 152 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_start" "D_Melpa_Madre_source_MadreView_mc_127_88_155_4_stop" ]
        %timeY.6 = local;
        symbol [ timeY %timeY.6 152 12 17 ];
        symbol [ _screenHeight %tmp.76 152 21 34 ];
        %tmp.76 = getv ? :_screenHeight;
        %tmp.77 = lgetv %timeTextDimensions.4;
        symbol [ timeTextDimensions %tmp.77 152 37 55 ];
        %tmp.78 = 1;
        %tmp.79 = agetv %tmp.77 %tmp.78;
        %tmp.80 = sub %tmp.76 %tmp.79;
        %tmp.81 = 2;
        %tmp.82 = div %tmp.80 %tmp.81;
        %tmp.83 = 20;
        %tmp.84 = sub %tmp.82 %tmp.83;
        lputv %timeY.6 %tmp.84;
        symbol [ timeY %timeY.6 152 12 17 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 154 8 ]
        %tmp.85 = lgetv %dc;
        symbol [ dc %tmp.85 154 8 10 ];
        symbol [ drawText %tmp.86 154 11 19 ];
        %tmp.86 = getv function %tmp.85 :drawText;
        %tmp.87 = lgetv %timeX.5;
        symbol [ timeX %tmp.87 154 20 25 ];
        %tmp.88 = lgetv %timeY.6;
        symbol [ timeY %tmp.88 154 27 32 ];
        symbol [ _timeFont %tmp.90 154 34 43 ];
        %tmp.90 = getv ? :_timeFont;
        %tmp.91 = lgetv %timeString.1;
        symbol [ timeString %tmp.91 154 45 55 ];
        symbol [ Graphics %tmp.92 154 57 65 ];
        %tmp.92 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_LEFT %tmp.93 154 66 83 ];
        %tmp.93 = getv %tmp.92 :TEXT_JUSTIFY_LEFT;
        invoke %tmp.85 %tmp.86(%tmp.87, %tmp.88, %tmp.90, %tmp.91, %tmp.93);
D_Melpa_Madre_source_MadreView_mc_127_88_155_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 157; @symbol_functiondef = [drawDate,157,22,30]; @symbol_param<0> = [dc,157,31,33]; @symbol_param<0>_type<0> = [Graphics,157,37,45]; @symbol_param<0>_type<1> = [Dc,157,46,48]; @symbol_param<1> = [date,157,50,54]; @symbol_param<1>_type<0> = [Gregorian,157,58,67]; @symbol_param<1>_type<1> = [Info,157,68,72]; ]
    private
    function drawDate(dc as Graphics.Dc, date as Gregorian.Info) as Void {
D_Melpa_Madre_source_MadreView_mc_157_82_168_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 158 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_157_82_168_4_start" "D_Melpa_Madre_source_MadreView_mc_157_82_168_4_stop" ]
        %dateString.1 = local;
        symbol [ dateString %dateString.1 158 12 22 ];
        symbol [ Lang %tmp.1 158 25 29 ];
        %tmp.1 = getm $.Toybox.Lang;
        symbol [ format %tmp.2 158 30 36 ];
        %tmp.2 = getv function %tmp.1 :format;
        %tmp.3 = "$1$ $2$";
        %tmp.4 = newa 2;
        %tmp.5 = lgetv %date;
        symbol [ date %tmp.5 159 12 16 ];
        symbol [ day_of_week %tmp.6 159 17 28 ];
        %tmp.6 = getv %tmp.5 :day_of_week;
        symbol [ toUpper %tmp.7 159 29 36 ];
        %tmp.7 = getv function %tmp.6 :toUpper;
        %tmp.8 = invoke %tmp.6 %tmp.7();
        %tmp.9 = dup %tmp.4;
        %tmp.10 = aputv %tmp.9 0 %tmp.8;
        %tmp.11 = lgetv %date;
        symbol [ date %tmp.11 160 12 16 ];
        symbol [ day %tmp.12 160 17 20 ];
        %tmp.12 = getv %tmp.11 :day;
        %tmp.13 = dup %tmp.10;
        %tmp.14 = aputv %tmp.13 1 %tmp.12;
        %tmp.15 = invoke %tmp.1 %tmp.2(%tmp.3, %tmp.14);
        lputv %dateString.1 %tmp.15;
        symbol [ dateString %dateString.1 158 12 22 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 163 8 ]
        %tmp.16 = lgetv %dc;
        symbol [ dc %tmp.16 163 8 10 ];
        symbol [ setColor %tmp.17 163 11 19 ];
        %tmp.17 = getv function %tmp.16 :setColor;
        symbol [ _foregroundColor %tmp.19 163 20 36 ];
        %tmp.19 = getv ? :_foregroundColor;
        symbol [ Graphics %tmp.20 163 38 46 ];
        %tmp.20 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.21 163 47 64 ];
        %tmp.21 = getv %tmp.20 :COLOR_TRANSPARENT;
        invoke %tmp.16 %tmp.17(%tmp.19, %tmp.21);
[ "D:\Melpa\Madre\source\MadreView.mc" 164 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_157_82_168_4_start" "D_Melpa_Madre_source_MadreView_mc_157_82_168_4_stop" ]
        %dateX.2 = local;
        symbol [ dateX %dateX.2 164 12 17 ];
        symbol [ _screenCenterPoint %tmp.23 164 20 38 ];
        %tmp.23 = getv ? :_screenCenterPoint;
        %tmp.24 = 0;
        %tmp.25 = agetv %tmp.23 %tmp.24;
        lputv %dateX.2 %tmp.25;
        symbol [ dateX %dateX.2 164 12 17 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 165 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_157_82_168_4_start" "D_Melpa_Madre_source_MadreView_mc_157_82_168_4_stop" ]
        %dateY.3 = local;
        symbol [ dateY %dateY.3 165 12 17 ];
        symbol [ _screenCenterPoint %tmp.27 165 20 38 ];
        %tmp.27 = getv ? :_screenCenterPoint;
        %tmp.28 = 1;
        %tmp.29 = agetv %tmp.27 %tmp.28;
        %tmp.30 = 40;
        %tmp.31 = add %tmp.29 %tmp.30;
        lputv %dateY.3 %tmp.31;
        symbol [ dateY %dateY.3 165 12 17 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 167 8 ]
        %tmp.32 = lgetv %dc;
        symbol [ dc %tmp.32 167 8 10 ];
        symbol [ drawText %tmp.33 167 11 19 ];
        %tmp.33 = getv function %tmp.32 :drawText;
        %tmp.34 = lgetv %dateX.2;
        symbol [ dateX %tmp.34 167 20 25 ];
        %tmp.35 = lgetv %dateY.3;
        symbol [ dateY %tmp.35 167 27 32 ];
        symbol [ _dataFont %tmp.37 167 34 43 ];
        %tmp.37 = getv ? :_dataFont;
        %tmp.38 = lgetv %dateString.1;
        symbol [ dateString %tmp.38 167 45 55 ];
        symbol [ Graphics %tmp.39 167 57 65 ];
        %tmp.39 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_CENTER %tmp.40 167 66 85 ];
        %tmp.40 = getv %tmp.39 :TEXT_JUSTIFY_CENTER;
        invoke %tmp.32 %tmp.33(%tmp.34, %tmp.35, %tmp.37, %tmp.38, %tmp.40);
D_Melpa_Madre_source_MadreView_mc_157_82_168_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 170; @symbol_functiondef = [drawActivityData,170,21,37]; @symbol_param<0> = [dc,170,38,40]; @symbol_param<0>_type<0> = [Graphics,170,44,52]; @symbol_param<0>_type<1> = [Dc,170,53,55]; ]
    private
    function drawActivityData(dc as Graphics.Dc) as Void {
D_Melpa_Madre_source_MadreView_mc_170_65_195_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 171 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_170_65_195_4_start" "D_Melpa_Madre_source_MadreView_mc_170_65_195_4_stop" ]
        %activityInfo.1 = local;
        symbol [ activityInfo %activityInfo.1 171 12 24 ];
        symbol [ ActivityMonitor %tmp.1 171 27 42 ];
        %tmp.1 = getm $.Toybox.ActivityMonitor;
        symbol [ getInfo %tmp.2 171 43 50 ];
        %tmp.2 = getv function %tmp.1 :getInfo;
        %tmp.3 = invoke %tmp.1 %tmp.2();
        lputv %activityInfo.1 %tmp.3;
        symbol [ activityInfo %activityInfo.1 171 12 24 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 172 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_170_65_195_4_start" "D_Melpa_Madre_source_MadreView_mc_170_65_195_4_stop" ]
        %yOffset.2 = local;
        symbol [ yOffset %yOffset.2 172 12 19 ];
        %tmp.4 = 80;
        lputv %yOffset.2 %tmp.4;
        symbol [ yOffset %yOffset.2 172 12 19 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 175 8 ]
D_Melpa_Madre_source_MadreView_mc_175_8_183_8_if_stmt:
D_Melpa_Madre_source_MadreView_mc_175_12_175_61_begin:
        %tmp.5 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.5 175 12 24 ];
        %tmp.7 = const :steps;
        symbol [ steps %tmp.7 175 30 35 const ];
        %tmp.8 = canhazplz %tmp.5 %tmp.7;
        bf %tmp.8 @D_Melpa_Madre_source_MadreView_mc_175_8_183_8_if_end;
D_Melpa_Madre_source_MadreView_mc_175_39_175_61_true:
        %tmp.9 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.9 175 39 51 ];
        %tmp.10 = as %tmp.9 { (interface { var steps; }) };
        symbol [ steps %tmp.11 175 52 57 ];
        %tmp.11 = getv %tmp.10 :steps;
        %tmp.12 = null;
        %tmp.13 = ne %tmp.11 %tmp.12;
        push %tmp.13;
D_Melpa_Madre_source_MadreView_mc_175_39_175_61_end:
        %tmp.14 = phi [%tmp.8 @D_Melpa_Madre_source_MadreView_mc_175_12_175_61_begin] [%tmp.13 @D_Melpa_Madre_source_MadreView_mc_175_39_175_61_true] [%tmp.14 @D_Melpa_Madre_source_MadreView_mc_175_39_175_61_end];
        bf %tmp.14 @D_Melpa_Madre_source_MadreView_mc_175_8_183_8_if_end;
D_Melpa_Madre_source_MadreView_mc_175_8_183_8_if_true:
D_Melpa_Madre_source_MadreView_mc_175_67_183_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 176 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_175_67_183_8_start" "D_Melpa_Madre_source_MadreView_mc_175_67_183_8_stop" ]
        %stepsString.3 = local;
        symbol [ stepsString %stepsString.3 176 16 27 ];
        %tmp.15 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.15 176 30 42 ];
        %tmp.16 = as %tmp.15 { (interface { var steps; }) };
        symbol [ steps %tmp.17 176 43 48 ];
        %tmp.17 = getv %tmp.16 :steps;
        symbol [ toString %tmp.18 176 49 57 ];
        %tmp.18 = getv function %tmp.17 :toString;
        %tmp.19 = invoke %tmp.17 %tmp.18();
        lputv %stepsString.3 %tmp.19;
        symbol [ stepsString %stepsString.3 176 16 27 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 177 12 ]
        %tmp.20 = lgetv %dc;
        symbol [ dc %tmp.20 177 12 14 ];
        symbol [ setColor %tmp.21 177 15 23 ];
        %tmp.21 = getv function %tmp.20 :setColor;
        symbol [ _foregroundColor %tmp.23 177 24 40 ];
        %tmp.23 = getv ? :_foregroundColor;
        symbol [ Graphics %tmp.24 177 42 50 ];
        %tmp.24 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.25 177 51 68 ];
        %tmp.25 = getv %tmp.24 :COLOR_TRANSPARENT;
        invoke %tmp.20 %tmp.21(%tmp.23, %tmp.25);
[ "D:\Melpa\Madre\source\MadreView.mc" 178 12 ]
        %tmp.26 = lgetv %dc;
        symbol [ dc %tmp.26 178 12 14 ];
        symbol [ drawText %tmp.27 178 15 23 ];
        %tmp.27 = getv function %tmp.26 :drawText;
        symbol [ _screenWidth %tmp.29 178 24 36 ];
        %tmp.29 = getv ? :_screenWidth;
        %tmp.30 = 10;
        %tmp.31 = sub %tmp.29 %tmp.30;
        %tmp.32 = lgetv %yOffset.2;
        symbol [ yOffset %tmp.32 178 43 50 ];
        symbol [ _smallFont %tmp.34 178 52 62 ];
        %tmp.34 = getv ? :_smallFont;
        %tmp.35 = lgetv %stepsString.3;
        symbol [ stepsString %tmp.35 178 64 75 ];
        symbol [ Graphics %tmp.36 178 77 85 ];
        %tmp.36 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_RIGHT %tmp.37 178 86 104 ];
        %tmp.37 = getv %tmp.36 :TEXT_JUSTIFY_RIGHT;
        invoke %tmp.26 %tmp.27(%tmp.31, %tmp.32, %tmp.34, %tmp.35, %tmp.37);
[ "D:\Melpa\Madre\source\MadreView.mc" 181 12 ]
        %tmp.38 = lgetv %dc;
        symbol [ dc %tmp.38 181 12 14 ];
        symbol [ setColor %tmp.39 181 15 23 ];
        %tmp.39 = getv function %tmp.38 :setColor;
        symbol [ _accentColor %tmp.41 181 24 36 ];
        %tmp.41 = getv ? :_accentColor;
        symbol [ Graphics %tmp.42 181 38 46 ];
        %tmp.42 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.43 181 47 64 ];
        %tmp.43 = getv %tmp.42 :COLOR_TRANSPARENT;
        invoke %tmp.38 %tmp.39(%tmp.41, %tmp.43);
[ "D:\Melpa\Madre\source\MadreView.mc" 182 12 ]
        %tmp.44 = lgetv %dc;
        symbol [ dc %tmp.44 182 12 14 ];
        symbol [ drawText %tmp.45 182 15 23 ];
        %tmp.45 = getv function %tmp.44 :drawText;
        symbol [ _screenWidth %tmp.47 182 24 36 ];
        %tmp.47 = getv ? :_screenWidth;
        %tmp.48 = 50;
        %tmp.49 = sub %tmp.47 %tmp.48;
        %tmp.50 = lgetv %yOffset.2;
        symbol [ yOffset %tmp.50 182 43 50 ];
        symbol [ _smallFont %tmp.52 182 52 62 ];
        %tmp.52 = getv ? :_smallFont;
        %tmp.53 = "?";
        symbol [ Graphics %tmp.54 182 69 77 ];
        %tmp.54 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_RIGHT %tmp.55 182 78 96 ];
        %tmp.55 = getv %tmp.54 :TEXT_JUSTIFY_RIGHT;
        invoke %tmp.44 %tmp.45(%tmp.49, %tmp.50, %tmp.52, %tmp.53, %tmp.55);
D_Melpa_Madre_source_MadreView_mc_175_67_183_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_175_8_183_8_if_end;
D_Melpa_Madre_source_MadreView_mc_175_8_183_8_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 186 8 ]
D_Melpa_Madre_source_MadreView_mc_186_8_194_8_if_stmt:
D_Melpa_Madre_source_MadreView_mc_186_12_186_67_begin:
        %tmp.56 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.56 186 12 24 ];
        %tmp.58 = const :calories;
        symbol [ calories %tmp.58 186 30 38 const ];
        %tmp.59 = canhazplz %tmp.56 %tmp.58;
        bf %tmp.59 @D_Melpa_Madre_source_MadreView_mc_186_8_194_8_if_end;
D_Melpa_Madre_source_MadreView_mc_186_42_186_67_true:
        %tmp.60 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.60 186 42 54 ];
        %tmp.61 = as %tmp.60 { (interface { var calories; }) };
        symbol [ calories %tmp.62 186 55 63 ];
        %tmp.62 = getv %tmp.61 :calories;
        %tmp.63 = null;
        %tmp.64 = ne %tmp.62 %tmp.63;
        push %tmp.64;
D_Melpa_Madre_source_MadreView_mc_186_42_186_67_end:
        %tmp.65 = phi [%tmp.59 @D_Melpa_Madre_source_MadreView_mc_186_12_186_67_begin] [%tmp.64 @D_Melpa_Madre_source_MadreView_mc_186_42_186_67_true] [%tmp.65 @D_Melpa_Madre_source_MadreView_mc_186_42_186_67_end];
        bf %tmp.65 @D_Melpa_Madre_source_MadreView_mc_186_8_194_8_if_end;
D_Melpa_Madre_source_MadreView_mc_186_8_194_8_if_true:
D_Melpa_Madre_source_MadreView_mc_186_73_194_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 187 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_186_73_194_8_start" "D_Melpa_Madre_source_MadreView_mc_186_73_194_8_stop" ]
        %caloriesString.4 = local;
        symbol [ caloriesString %caloriesString.4 187 16 30 ];
        %tmp.66 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.66 187 33 45 ];
        %tmp.67 = as %tmp.66 { (interface { var calories; }) };
        symbol [ calories %tmp.68 187 46 54 ];
        %tmp.68 = getv %tmp.67 :calories;
        symbol [ toString %tmp.69 187 55 63 ];
        %tmp.69 = getv function %tmp.68 :toString;
        %tmp.70 = invoke %tmp.68 %tmp.69();
        lputv %caloriesString.4 %tmp.70;
        symbol [ caloriesString %caloriesString.4 187 16 30 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 188 12 ]
        %tmp.71 = lgetv %dc;
        symbol [ dc %tmp.71 188 12 14 ];
        symbol [ setColor %tmp.72 188 15 23 ];
        %tmp.72 = getv function %tmp.71 :setColor;
        symbol [ _foregroundColor %tmp.74 188 24 40 ];
        %tmp.74 = getv ? :_foregroundColor;
        symbol [ Graphics %tmp.75 188 42 50 ];
        %tmp.75 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.76 188 51 68 ];
        %tmp.76 = getv %tmp.75 :COLOR_TRANSPARENT;
        invoke %tmp.71 %tmp.72(%tmp.74, %tmp.76);
[ "D:\Melpa\Madre\source\MadreView.mc" 189 12 ]
        %tmp.77 = lgetv %dc;
        symbol [ dc %tmp.77 189 12 14 ];
        symbol [ drawText %tmp.78 189 15 23 ];
        %tmp.78 = getv function %tmp.77 :drawText;
        symbol [ _screenWidth %tmp.80 189 24 36 ];
        %tmp.80 = getv ? :_screenWidth;
        %tmp.81 = 10;
        %tmp.82 = sub %tmp.80 %tmp.81;
        %tmp.83 = lgetv %yOffset.2;
        symbol [ yOffset %tmp.83 189 43 50 ];
        %tmp.84 = 25;
        %tmp.85 = add %tmp.83 %tmp.84;
        symbol [ _smallFont %tmp.87 189 57 67 ];
        %tmp.87 = getv ? :_smallFont;
        %tmp.88 = lgetv %caloriesString.4;
        symbol [ caloriesString %tmp.88 189 69 83 ];
        symbol [ Graphics %tmp.89 189 85 93 ];
        %tmp.89 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_RIGHT %tmp.90 189 94 112 ];
        %tmp.90 = getv %tmp.89 :TEXT_JUSTIFY_RIGHT;
        invoke %tmp.77 %tmp.78(%tmp.82, %tmp.85, %tmp.87, %tmp.88, %tmp.90);
[ "D:\Melpa\Madre\source\MadreView.mc" 192 12 ]
        %tmp.91 = lgetv %dc;
        symbol [ dc %tmp.91 192 12 14 ];
        symbol [ setColor %tmp.92 192 15 23 ];
        %tmp.92 = getv function %tmp.91 :setColor;
        symbol [ _accentColor %tmp.94 192 24 36 ];
        %tmp.94 = getv ? :_accentColor;
        symbol [ Graphics %tmp.95 192 38 46 ];
        %tmp.95 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.96 192 47 64 ];
        %tmp.96 = getv %tmp.95 :COLOR_TRANSPARENT;
        invoke %tmp.91 %tmp.92(%tmp.94, %tmp.96);
[ "D:\Melpa\Madre\source\MadreView.mc" 193 12 ]
        %tmp.97 = lgetv %dc;
        symbol [ dc %tmp.97 193 12 14 ];
        symbol [ drawText %tmp.98 193 15 23 ];
        %tmp.98 = getv function %tmp.97 :drawText;
        symbol [ _screenWidth %tmp.100 193 24 36 ];
        %tmp.100 = getv ? :_screenWidth;
        %tmp.101 = 50;
        %tmp.102 = sub %tmp.100 %tmp.101;
        %tmp.103 = lgetv %yOffset.2;
        symbol [ yOffset %tmp.103 193 43 50 ];
        %tmp.104 = 25;
        %tmp.105 = add %tmp.103 %tmp.104;
        symbol [ _smallFont %tmp.107 193 57 67 ];
        %tmp.107 = getv ? :_smallFont;
        %tmp.108 = "?";
        symbol [ Graphics %tmp.109 193 74 82 ];
        %tmp.109 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_RIGHT %tmp.110 193 83 101 ];
        %tmp.110 = getv %tmp.109 :TEXT_JUSTIFY_RIGHT;
        invoke %tmp.97 %tmp.98(%tmp.102, %tmp.105, %tmp.107, %tmp.108, %tmp.110);
D_Melpa_Madre_source_MadreView_mc_186_73_194_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_186_8_194_8_if_end;
D_Melpa_Madre_source_MadreView_mc_186_8_194_8_if_end:
D_Melpa_Madre_source_MadreView_mc_170_65_195_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 197; @symbol_functiondef = [drawBattery,197,22,33]; @symbol_param<0> = [dc,197,34,36]; @symbol_param<0>_type<0> = [Graphics,197,40,48]; @symbol_param<0>_type<1> = [Dc,197,49,51]; ]
    private
    function drawBattery(dc as Graphics.Dc) as Void {
D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 198 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start" "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop" ]
        %battery.1 = local;
        symbol [ battery %battery.1 198 12 19 ];
        symbol [ System %tmp.1 198 22 28 ];
        %tmp.1 = getm $.Toybox.System;
        symbol [ getSystemStats %tmp.2 198 29 43 ];
        %tmp.2 = getv function %tmp.1 :getSystemStats;
        %tmp.3 = invoke %tmp.1 %tmp.2();
        symbol [ battery %tmp.4 198 46 53 ];
        %tmp.4 = getv %tmp.3 :battery;
        lputv %battery.1 %tmp.4;
        symbol [ battery %battery.1 198 12 19 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 199 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start" "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop" ]
        %batteryString.2 = local;
        symbol [ batteryString %batteryString.2 199 12 25 ];
        symbol [ Lang %tmp.5 199 28 32 ];
        %tmp.5 = getm $.Toybox.Lang;
        symbol [ format %tmp.6 199 33 39 ];
        %tmp.6 = getv function %tmp.5 :format;
        %tmp.7 = "$1$%";
        %tmp.8 = newa 1;
        %tmp.9 = lgetv %battery.1;
        symbol [ battery %tmp.9 199 49 56 ];
        symbol [ format %tmp.10 199 57 63 ];
        %tmp.10 = getv function %tmp.9 :format;
        %tmp.11 = "%.0f";
        %tmp.12 = invoke %tmp.9 %tmp.10(%tmp.11);
        %tmp.13 = dup %tmp.8;
        %tmp.14 = aputv %tmp.13 0 %tmp.12;
        %tmp.15 = invoke %tmp.5 %tmp.6(%tmp.7, %tmp.14);
        lputv %batteryString.2 %tmp.15;
        symbol [ batteryString %batteryString.2 199 12 25 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 201 8 ]
        %tmp.16 = lgetv %dc;
        symbol [ dc %tmp.16 201 8 10 ];
        symbol [ setColor %tmp.17 201 11 19 ];
        %tmp.17 = getv function %tmp.16 :setColor;
        symbol [ _foregroundColor %tmp.19 201 20 36 ];
        %tmp.19 = getv ? :_foregroundColor;
        symbol [ Graphics %tmp.20 201 38 46 ];
        %tmp.20 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.21 201 47 64 ];
        %tmp.21 = getv %tmp.20 :COLOR_TRANSPARENT;
        invoke %tmp.16 %tmp.17(%tmp.19, %tmp.21);
[ "D:\Melpa\Madre\source\MadreView.mc" 202 8 ]
        %tmp.22 = lgetv %dc;
        symbol [ dc %tmp.22 202 8 10 ];
        symbol [ drawText %tmp.23 202 11 19 ];
        %tmp.23 = getv function %tmp.22 :drawText;
        %tmp.24 = 10;
        %tmp.25 = 80;
        symbol [ _smallFont %tmp.27 202 28 38 ];
        %tmp.27 = getv ? :_smallFont;
        %tmp.28 = lgetv %batteryString.2;
        symbol [ batteryString %tmp.28 202 40 53 ];
        symbol [ Graphics %tmp.29 202 55 63 ];
        %tmp.29 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_LEFT %tmp.30 202 64 81 ];
        %tmp.30 = getv %tmp.29 :TEXT_JUSTIFY_LEFT;
        invoke %tmp.22 %tmp.23(%tmp.24, %tmp.25, %tmp.27, %tmp.28, %tmp.30);
[ "D:\Melpa\Madre\source\MadreView.mc" 205 8 ]
        %tmp.31 = lgetv %dc;
        symbol [ dc %tmp.31 205 8 10 ];
        symbol [ setColor %tmp.32 205 11 19 ];
        %tmp.32 = getv function %tmp.31 :setColor;
        symbol [ _accentColor %tmp.34 205 20 32 ];
        %tmp.34 = getv ? :_accentColor;
        symbol [ Graphics %tmp.35 205 34 42 ];
        %tmp.35 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.36 205 43 60 ];
        %tmp.36 = getv %tmp.35 :COLOR_TRANSPARENT;
        invoke %tmp.31 %tmp.32(%tmp.34, %tmp.36);
[ "D:\Melpa\Madre\source\MadreView.mc" 206 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start" "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop" ]
        %batteryWidth.3 = local;
        symbol [ batteryWidth %batteryWidth.3 206 12 24 ];
        %tmp.37 = 20;
        lputv %batteryWidth.3 %tmp.37;
        symbol [ batteryWidth %batteryWidth.3 206 12 24 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 207 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start" "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop" ]
        %batteryHeight.4 = local;
        symbol [ batteryHeight %batteryHeight.4 207 12 25 ];
        %tmp.38 = 10;
        lputv %batteryHeight.4 %tmp.38;
        symbol [ batteryHeight %batteryHeight.4 207 12 25 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 208 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start" "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop" ]
        %batteryX.5 = local;
        symbol [ batteryX %batteryX.5 208 12 20 ];
        %tmp.39 = 10;
        lputv %batteryX.5 %tmp.39;
        symbol [ batteryX %batteryX.5 208 12 20 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 209 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start" "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop" ]
        %batteryY.6 = local;
        symbol [ batteryY %batteryY.6 209 12 20 ];
        %tmp.40 = 100;
        lputv %batteryY.6 %tmp.40;
        symbol [ batteryY %batteryY.6 209 12 20 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 212 8 ]
        %tmp.41 = lgetv %dc;
        symbol [ dc %tmp.41 212 8 10 ];
        symbol [ drawRectangle %tmp.42 212 11 24 ];
        %tmp.42 = getv function %tmp.41 :drawRectangle;
        %tmp.43 = lgetv %batteryX.5;
        symbol [ batteryX %tmp.43 212 25 33 ];
        %tmp.44 = lgetv %batteryY.6;
        symbol [ batteryY %tmp.44 212 35 43 ];
        %tmp.45 = lgetv %batteryWidth.3;
        symbol [ batteryWidth %tmp.45 212 45 57 ];
        %tmp.46 = lgetv %batteryHeight.4;
        symbol [ batteryHeight %tmp.46 212 59 72 ];
        invoke %tmp.41 %tmp.42(%tmp.43, %tmp.44, %tmp.45, %tmp.46);
[ "D:\Melpa\Madre\source\MadreView.mc" 213 8 ]
        %tmp.47 = lgetv %dc;
        symbol [ dc %tmp.47 213 8 10 ];
        symbol [ drawRectangle %tmp.48 213 11 24 ];
        %tmp.48 = getv function %tmp.47 :drawRectangle;
        %tmp.49 = lgetv %batteryX.5;
        symbol [ batteryX %tmp.49 213 25 33 ];
        %tmp.50 = lgetv %batteryWidth.3;
        symbol [ batteryWidth %tmp.50 213 36 48 ];
        %tmp.51 = add %tmp.49 %tmp.50;
        %tmp.52 = lgetv %batteryY.6;
        symbol [ batteryY %tmp.52 213 50 58 ];
        %tmp.53 = 2;
        %tmp.54 = add %tmp.52 %tmp.53;
        %tmp.55 = 3;
        %tmp.56 = lgetv %batteryHeight.4;
        symbol [ batteryHeight %tmp.56 213 67 80 ];
        %tmp.57 = 4;
        %tmp.58 = sub %tmp.56 %tmp.57;
        invoke %tmp.47 %tmp.48(%tmp.51, %tmp.54, %tmp.55, %tmp.58);
[ "D:\Melpa\Madre\source\MadreView.mc" 216 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_start" "D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop" ]
        %fillWidth.7 = local;
        symbol [ fillWidth %fillWidth.7 216 12 21 ];
        %tmp.59 = lgetv %batteryWidth.3;
        symbol [ batteryWidth %tmp.59 216 25 37 ];
        %tmp.60 = 2;
        %tmp.61 = sub %tmp.59 %tmp.60;
        %tmp.62 = lgetv %battery.1;
        symbol [ battery %tmp.62 216 45 52 ];
        %tmp.63 = mul %tmp.61 %tmp.62;
        %tmp.64 = 100;
        %tmp.65 = div %tmp.63 %tmp.64;
        lputv %fillWidth.7 %tmp.65;
        symbol [ fillWidth %fillWidth.7 216 12 21 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 217 8 ]
D_Melpa_Madre_source_MadreView_mc_217_8_221_8_if_stmt:
        %tmp.66 = lgetv %battery.1;
        symbol [ battery %tmp.66 217 12 19 ];
        %tmp.67 = 20;
        %tmp.68 = gt %tmp.66 %tmp.67;
        bf %tmp.68 @D_Melpa_Madre_source_MadreView_mc_217_8_221_8_if_else_false;
D_Melpa_Madre_source_MadreView_mc_217_8_221_8_if_true:
D_Melpa_Madre_source_MadreView_mc_217_26_219_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 218 12 ]
        %tmp.69 = lgetv %dc;
        symbol [ dc %tmp.69 218 12 14 ];
        symbol [ setColor %tmp.70 218 15 23 ];
        %tmp.70 = getv function %tmp.69 :setColor;
        symbol [ Graphics %tmp.71 218 24 32 ];
        %tmp.71 = getm $.Toybox.Graphics;
        symbol [ COLOR_GREEN %tmp.72 218 33 44 ];
        %tmp.72 = getv %tmp.71 :COLOR_GREEN;
        symbol [ Graphics %tmp.73 218 46 54 ];
        %tmp.73 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.74 218 55 72 ];
        %tmp.74 = getv %tmp.73 :COLOR_TRANSPARENT;
        invoke %tmp.69 %tmp.70(%tmp.72, %tmp.74);
D_Melpa_Madre_source_MadreView_mc_217_26_219_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_217_8_221_8_if_end;
D_Melpa_Madre_source_MadreView_mc_217_8_221_8_if_else_false:
D_Melpa_Madre_source_MadreView_mc_219_15_221_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 220 12 ]
        %tmp.75 = lgetv %dc;
        symbol [ dc %tmp.75 220 12 14 ];
        symbol [ setColor %tmp.76 220 15 23 ];
        %tmp.76 = getv function %tmp.75 :setColor;
        symbol [ Graphics %tmp.77 220 24 32 ];
        %tmp.77 = getm $.Toybox.Graphics;
        symbol [ COLOR_RED %tmp.78 220 33 42 ];
        %tmp.78 = getv %tmp.77 :COLOR_RED;
        symbol [ Graphics %tmp.79 220 44 52 ];
        %tmp.79 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.80 220 53 70 ];
        %tmp.80 = getv %tmp.79 :COLOR_TRANSPARENT;
        invoke %tmp.75 %tmp.76(%tmp.78, %tmp.80);
D_Melpa_Madre_source_MadreView_mc_219_15_221_8_stop:
D_Melpa_Madre_source_MadreView_mc_217_8_221_8_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 222 8 ]
        %tmp.81 = lgetv %dc;
        symbol [ dc %tmp.81 222 8 10 ];
        symbol [ fillRectangle %tmp.82 222 11 24 ];
        %tmp.82 = getv function %tmp.81 :fillRectangle;
        %tmp.83 = lgetv %batteryX.5;
        symbol [ batteryX %tmp.83 222 25 33 ];
        %tmp.84 = 1;
        %tmp.85 = add %tmp.83 %tmp.84;
        %tmp.86 = lgetv %batteryY.6;
        symbol [ batteryY %tmp.86 222 39 47 ];
        %tmp.87 = 1;
        %tmp.88 = add %tmp.86 %tmp.87;
        %tmp.89 = lgetv %fillWidth.7;
        symbol [ fillWidth %tmp.89 222 53 62 ];
        %tmp.90 = lgetv %batteryHeight.4;
        symbol [ batteryHeight %tmp.90 222 64 77 ];
        %tmp.91 = 2;
        %tmp.92 = sub %tmp.90 %tmp.91;
        invoke %tmp.81 %tmp.82(%tmp.85, %tmp.88, %tmp.89, %tmp.92);
D_Melpa_Madre_source_MadreView_mc_197_61_223_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 225; @symbol_functiondef = [drawHeartRate,225,21,34]; @symbol_param<0> = [dc,225,35,37]; @symbol_param<0>_type<0> = [Graphics,225,41,49]; @symbol_param<0>_type<1> = [Dc,225,50,52]; ]
    private
    function drawHeartRate(dc as Graphics.Dc) as Void {
D_Melpa_Madre_source_MadreView_mc_225_62_237_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 226 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_225_62_237_4_start" "D_Melpa_Madre_source_MadreView_mc_225_62_237_4_stop" ]
        %activityInfo.1 = local;
        symbol [ activityInfo %activityInfo.1 226 12 24 ];
        symbol [ ActivityMonitor %tmp.1 226 27 42 ];
        %tmp.1 = getm $.Toybox.ActivityMonitor;
        symbol [ getInfo %tmp.2 226 43 50 ];
        %tmp.2 = getv function %tmp.1 :getInfo;
        %tmp.3 = invoke %tmp.1 %tmp.2();
        lputv %activityInfo.1 %tmp.3;
        symbol [ activityInfo %activityInfo.1 226 12 24 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 228 8 ]
D_Melpa_Madre_source_MadreView_mc_228_8_236_8_if_stmt:
D_Melpa_Madre_source_MadreView_mc_228_12_228_83_begin:
        %tmp.4 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.4 228 12 24 ];
        %tmp.6 = const :currentHeartRate;
        symbol [ currentHeartRate %tmp.6 228 30 46 const ];
        %tmp.7 = canhazplz %tmp.4 %tmp.6;
        bf %tmp.7 @D_Melpa_Madre_source_MadreView_mc_228_8_236_8_if_end;
D_Melpa_Madre_source_MadreView_mc_228_50_228_83_true:
        %tmp.8 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.8 228 50 62 ];
        %tmp.9 = as %tmp.8 { (interface { var currentHeartRate; }) };
        symbol [ currentHeartRate %tmp.10 228 63 79 ];
        %tmp.10 = getv %tmp.9 :currentHeartRate;
        %tmp.11 = null;
        %tmp.12 = ne %tmp.10 %tmp.11;
        push %tmp.12;
D_Melpa_Madre_source_MadreView_mc_228_50_228_83_end:
        %tmp.13 = phi [%tmp.7 @D_Melpa_Madre_source_MadreView_mc_228_12_228_83_begin] [%tmp.12 @D_Melpa_Madre_source_MadreView_mc_228_50_228_83_true] [%tmp.13 @D_Melpa_Madre_source_MadreView_mc_228_50_228_83_end];
        bf %tmp.13 @D_Melpa_Madre_source_MadreView_mc_228_8_236_8_if_end;
D_Melpa_Madre_source_MadreView_mc_228_8_236_8_if_true:
D_Melpa_Madre_source_MadreView_mc_228_89_236_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 229 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_228_89_236_8_start" "D_Melpa_Madre_source_MadreView_mc_228_89_236_8_stop" ]
        %hrString.2 = local;
        symbol [ hrString %hrString.2 229 16 24 ];
        %tmp.14 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.14 229 27 39 ];
        %tmp.15 = as %tmp.14 { (interface { var currentHeartRate; }) };
        symbol [ currentHeartRate %tmp.16 229 40 56 ];
        %tmp.16 = getv %tmp.15 :currentHeartRate;
        symbol [ toString %tmp.17 229 57 65 ];
        %tmp.17 = getv function %tmp.16 :toString;
        %tmp.18 = invoke %tmp.16 %tmp.17();
        lputv %hrString.2 %tmp.18;
        symbol [ hrString %hrString.2 229 16 24 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 230 12 ]
        %tmp.19 = lgetv %dc;
        symbol [ dc %tmp.19 230 12 14 ];
        symbol [ setColor %tmp.20 230 15 23 ];
        %tmp.20 = getv function %tmp.19 :setColor;
        symbol [ _foregroundColor %tmp.22 230 24 40 ];
        %tmp.22 = getv ? :_foregroundColor;
        symbol [ Graphics %tmp.23 230 42 50 ];
        %tmp.23 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.24 230 51 68 ];
        %tmp.24 = getv %tmp.23 :COLOR_TRANSPARENT;
        invoke %tmp.19 %tmp.20(%tmp.22, %tmp.24);
[ "D:\Melpa\Madre\source\MadreView.mc" 231 12 ]
        %tmp.25 = lgetv %dc;
        symbol [ dc %tmp.25 231 12 14 ];
        symbol [ drawText %tmp.26 231 15 23 ];
        %tmp.26 = getv function %tmp.25 :drawText;
        symbol [ _screenCenterPoint %tmp.28 231 24 42 ];
        %tmp.28 = getv ? :_screenCenterPoint;
        %tmp.29 = 0;
        %tmp.30 = agetv %tmp.28 %tmp.29;
        %tmp.31 = 50;
        %tmp.32 = add %tmp.30 %tmp.31;
        %tmp.33 = 30;
        symbol [ _smallFont %tmp.35 231 56 66 ];
        %tmp.35 = getv ? :_smallFont;
        %tmp.36 = lgetv %hrString.2;
        symbol [ hrString %tmp.36 231 68 76 ];
        symbol [ Graphics %tmp.37 231 78 86 ];
        %tmp.37 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_LEFT %tmp.38 231 87 104 ];
        %tmp.38 = getv %tmp.37 :TEXT_JUSTIFY_LEFT;
        invoke %tmp.25 %tmp.26(%tmp.32, %tmp.33, %tmp.35, %tmp.36, %tmp.38);
[ "D:\Melpa\Madre\source\MadreView.mc" 234 12 ]
        %tmp.39 = lgetv %dc;
        symbol [ dc %tmp.39 234 12 14 ];
        symbol [ setColor %tmp.40 234 15 23 ];
        %tmp.40 = getv function %tmp.39 :setColor;
        symbol [ Graphics %tmp.41 234 24 32 ];
        %tmp.41 = getm $.Toybox.Graphics;
        symbol [ COLOR_RED %tmp.42 234 33 42 ];
        %tmp.42 = getv %tmp.41 :COLOR_RED;
        symbol [ Graphics %tmp.43 234 44 52 ];
        %tmp.43 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.44 234 53 70 ];
        %tmp.44 = getv %tmp.43 :COLOR_TRANSPARENT;
        invoke %tmp.39 %tmp.40(%tmp.42, %tmp.44);
[ "D:\Melpa\Madre\source\MadreView.mc" 235 12 ]
        %tmp.45 = lgetv %dc;
        symbol [ dc %tmp.45 235 12 14 ];
        symbol [ drawText %tmp.46 235 15 23 ];
        %tmp.46 = getv function %tmp.45 :drawText;
        symbol [ _screenCenterPoint %tmp.48 235 24 42 ];
        %tmp.48 = getv ? :_screenCenterPoint;
        %tmp.49 = 0;
        %tmp.50 = agetv %tmp.48 %tmp.49;
        %tmp.51 = 20;
        %tmp.52 = add %tmp.50 %tmp.51;
        %tmp.53 = 30;
        symbol [ _smallFont %tmp.55 235 56 66 ];
        %tmp.55 = getv ? :_smallFont;
        %tmp.56 = "?";
        symbol [ Graphics %tmp.57 235 73 81 ];
        %tmp.57 = getm $.Toybox.Graphics;
        symbol [ TEXT_JUSTIFY_LEFT %tmp.58 235 82 99 ];
        %tmp.58 = getv %tmp.57 :TEXT_JUSTIFY_LEFT;
        invoke %tmp.45 %tmp.46(%tmp.52, %tmp.53, %tmp.55, %tmp.56, %tmp.58);
D_Melpa_Madre_source_MadreView_mc_228_89_236_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_228_8_236_8_if_end;
D_Melpa_Madre_source_MadreView_mc_228_8_236_8_if_end:
D_Melpa_Madre_source_MadreView_mc_225_62_237_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 239; @symbol_functiondef = [drawSteps,239,21,30]; @symbol_param<0> = [dc,239,31,33]; @symbol_param<0>_type<0> = [Graphics,239,37,45]; @symbol_param<0>_type<1> = [Dc,239,46,48]; ]
    private
    function drawSteps(dc as Graphics.Dc) as Void {
D_Melpa_Madre_source_MadreView_mc_239_58_269_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 240 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_239_58_269_4_start" "D_Melpa_Madre_source_MadreView_mc_239_58_269_4_stop" ]
        %activityInfo.1 = local;
        symbol [ activityInfo %activityInfo.1 240 12 24 ];
        symbol [ ActivityMonitor %tmp.1 240 27 42 ];
        %tmp.1 = getm $.Toybox.ActivityMonitor;
        symbol [ getInfo %tmp.2 240 43 50 ];
        %tmp.2 = getv function %tmp.1 :getInfo;
        %tmp.3 = invoke %tmp.1 %tmp.2();
        lputv %activityInfo.1 %tmp.3;
        symbol [ activityInfo %activityInfo.1 240 12 24 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 242 8 ]
D_Melpa_Madre_source_MadreView_mc_242_8_268_8_if_stmt:
D_Melpa_Madre_source_MadreView_mc_242_12_242_61_begin:
        %tmp.4 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.4 242 12 24 ];
        %tmp.6 = const :steps;
        symbol [ steps %tmp.6 242 30 35 const ];
        %tmp.7 = canhazplz %tmp.4 %tmp.6;
        bf %tmp.7 @D_Melpa_Madre_source_MadreView_mc_242_8_268_8_if_end;
D_Melpa_Madre_source_MadreView_mc_242_39_242_61_true:
        %tmp.8 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.8 242 39 51 ];
        %tmp.9 = as %tmp.8 { (interface { var steps; }) };
        symbol [ steps %tmp.10 242 52 57 ];
        %tmp.10 = getv %tmp.9 :steps;
        %tmp.11 = null;
        %tmp.12 = ne %tmp.10 %tmp.11;
        push %tmp.12;
D_Melpa_Madre_source_MadreView_mc_242_39_242_61_end:
        %tmp.13 = phi [%tmp.7 @D_Melpa_Madre_source_MadreView_mc_242_12_242_61_begin] [%tmp.12 @D_Melpa_Madre_source_MadreView_mc_242_39_242_61_true] [%tmp.13 @D_Melpa_Madre_source_MadreView_mc_242_39_242_61_end];
        bf %tmp.13 @D_Melpa_Madre_source_MadreView_mc_242_8_268_8_if_end;
D_Melpa_Madre_source_MadreView_mc_242_8_268_8_if_true:
D_Melpa_Madre_source_MadreView_mc_242_67_268_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 243 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_242_67_268_8_start" "D_Melpa_Madre_source_MadreView_mc_242_67_268_8_stop" ]
        %goal.2 = local;
        symbol [ goal %goal.2 243 16 20 ];
        %tmp.14 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.14 243 23 35 ];
        %tmp.15 = as %tmp.14 { (interface { var steps; }) };
        symbol [ stepGoal %tmp.16 243 36 44 ];
        %tmp.16 = getv %tmp.15 :stepGoal;
        lputv %goal.2 %tmp.16;
        symbol [ goal %goal.2 243 16 20 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 244 12 ]
D_Melpa_Madre_source_MadreView_mc_244_12_267_12_if_stmt:
D_Melpa_Madre_source_MadreView_mc_244_16_244_39_begin:
        %tmp.17 = lgetv %goal.2;
        symbol [ goal %tmp.17 244 16 20 ];
        %tmp.18 = null;
        %tmp.19 = ne %tmp.17 %tmp.18;
        bf %tmp.19 @D_Melpa_Madre_source_MadreView_mc_244_12_267_12_if_end;
D_Melpa_Madre_source_MadreView_mc_244_32_244_39_true:
        %tmp.20 = lgetv %goal.2;
        symbol [ goal %tmp.20 244 32 36 ];
        %tmp.21 = as %tmp.20 { (!Null) };
        %tmp.22 = 0;
        %tmp.23 = gt %tmp.21 %tmp.22;
        push %tmp.23;
D_Melpa_Madre_source_MadreView_mc_244_32_244_39_end:
        %tmp.24 = phi [%tmp.19 @D_Melpa_Madre_source_MadreView_mc_244_16_244_39_begin] [%tmp.23 @D_Melpa_Madre_source_MadreView_mc_244_32_244_39_true] [%tmp.24 @D_Melpa_Madre_source_MadreView_mc_244_32_244_39_end];
        bf %tmp.24 @D_Melpa_Madre_source_MadreView_mc_244_12_267_12_if_end;
D_Melpa_Madre_source_MadreView_mc_244_12_267_12_if_true:
D_Melpa_Madre_source_MadreView_mc_244_42_267_12_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 245 16 ]
        [ "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_start" "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_stop" ]
        %progress.3 = local;
        symbol [ progress %progress.3 245 20 28 ];
        %tmp.25 = lgetv %activityInfo.1;
        symbol [ activityInfo %tmp.25 245 31 43 ];
        %tmp.26 = as %tmp.25 { (interface { var steps; }) };
        symbol [ steps %tmp.27 245 44 49 ];
        %tmp.27 = getv %tmp.26 :steps;
        symbol [ toFloat %tmp.28 245 50 57 ];
        %tmp.28 = getv function %tmp.27 :toFloat;
        %tmp.29 = invoke %tmp.27 %tmp.28();
        %tmp.30 = lgetv %goal.2;
        symbol [ goal %tmp.30 245 62 66 ];
        %tmp.31 = as %tmp.30 { (!Null) };
        symbol [ toFloat %tmp.32 245 67 74 ];
        %tmp.32 = getv function %tmp.31 :toFloat;
        %tmp.33 = invoke %tmp.31 %tmp.32();
        %tmp.34 = div %tmp.29 %tmp.33;
        lputv %progress.3 %tmp.34;
        symbol [ progress %progress.3 245 20 28 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 246 16 ]
D_Melpa_Madre_source_MadreView_mc_246_16_248_16_if_stmt:
        %tmp.35 = lgetv %progress.3;
        symbol [ progress %tmp.35 246 20 28 ];
        %tmp.36 = 1.0;
        %tmp.37 = gt %tmp.35 %tmp.36;
        bf %tmp.37 @D_Melpa_Madre_source_MadreView_mc_246_16_248_16_if_end;
D_Melpa_Madre_source_MadreView_mc_246_16_248_16_if_true:
D_Melpa_Madre_source_MadreView_mc_246_36_248_16_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 247 20 ]
        %tmp.38 = 1.0;
        lputv %progress.3 %tmp.38;
        symbol [ progress %progress.3 247 20 28 ];
D_Melpa_Madre_source_MadreView_mc_246_36_248_16_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_246_16_248_16_if_end;
D_Melpa_Madre_source_MadreView_mc_246_16_248_16_if_end:
[ "D:\Melpa\Madre\source\MadreView.mc" 251 16 ]
        [ "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_start" "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_stop" ]
        %centerX.4 = local;
        symbol [ centerX %centerX.4 251 20 27 ];
        symbol [ _screenCenterPoint %tmp.40 251 30 48 ];
        %tmp.40 = getv ? :_screenCenterPoint;
        %tmp.41 = 0;
        %tmp.42 = agetv %tmp.40 %tmp.41;
        lputv %centerX.4 %tmp.42;
        symbol [ centerX %centerX.4 251 20 27 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 252 16 ]
        [ "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_start" "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_stop" ]
        %centerY.5 = local;
        symbol [ centerY %centerY.5 252 20 27 ];
        symbol [ _screenCenterPoint %tmp.44 252 30 48 ];
        %tmp.44 = getv ? :_screenCenterPoint;
        %tmp.45 = 1;
        %tmp.46 = agetv %tmp.44 %tmp.45;
        lputv %centerY.5 %tmp.46;
        symbol [ centerY %centerY.5 252 20 27 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 253 16 ]
        [ "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_start" "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_stop" ]
        %radius.6 = local;
        symbol [ radius %radius.6 253 20 26 ];
        %tmp.47 = 90;
        lputv %radius.6 %tmp.47;
        symbol [ radius %radius.6 253 20 26 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 254 16 ]
        [ "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_start" "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_stop" ]
        %startAngle.7 = local;
        symbol [ startAngle %startAngle.7 254 20 30 ];
        %tmp.48 = -90;
        lputv %startAngle.7 %tmp.48;
        symbol [ startAngle %startAngle.7 254 20 30 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 255 16 ]
        [ "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_start" "D_Melpa_Madre_source_MadreView_mc_244_42_267_12_stop" ]
        %sweepAngle.8 = local;
        symbol [ sweepAngle %sweepAngle.8 255 20 30 ];
        %tmp.49 = 360;
        %tmp.50 = lgetv %progress.3;
        symbol [ progress %tmp.50 255 39 47 ];
        %tmp.51 = mul %tmp.49 %tmp.50;
        lputv %sweepAngle.8 %tmp.51;
        symbol [ sweepAngle %sweepAngle.8 255 20 30 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 257 16 ]
        %tmp.52 = lgetv %dc;
        symbol [ dc %tmp.52 257 16 18 ];
        symbol [ setColor %tmp.53 257 19 27 ];
        %tmp.53 = getv function %tmp.52 :setColor;
        symbol [ _accentColor %tmp.55 257 28 40 ];
        %tmp.55 = getv ? :_accentColor;
        symbol [ Graphics %tmp.56 257 42 50 ];
        %tmp.56 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.57 257 51 68 ];
        %tmp.57 = getv %tmp.56 :COLOR_TRANSPARENT;
        invoke %tmp.52 %tmp.53(%tmp.55, %tmp.57);
[ "D:\Melpa\Madre\source\MadreView.mc" 258 16 ]
        %tmp.58 = lgetv %dc;
        symbol [ dc %tmp.58 258 16 18 ];
        symbol [ setPenWidth %tmp.59 258 19 30 ];
        %tmp.59 = getv function %tmp.58 :setPenWidth;
        %tmp.60 = 4;
        invoke %tmp.58 %tmp.59(%tmp.60);
[ "D:\Melpa\Madre\source\MadreView.mc" 261 16 ]
        %tmp.61 = lgetv %dc;
        symbol [ dc %tmp.61 261 16 18 ];
        symbol [ setColor %tmp.62 261 19 27 ];
        %tmp.62 = getv function %tmp.61 :setColor;
        symbol [ Graphics %tmp.63 261 28 36 ];
        %tmp.63 = getm $.Toybox.Graphics;
        symbol [ COLOR_DK_GRAY %tmp.64 261 37 50 ];
        %tmp.64 = getv %tmp.63 :COLOR_DK_GRAY;
        symbol [ Graphics %tmp.65 261 52 60 ];
        %tmp.65 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.66 261 61 78 ];
        %tmp.66 = getv %tmp.65 :COLOR_TRANSPARENT;
        invoke %tmp.61 %tmp.62(%tmp.64, %tmp.66);
[ "D:\Melpa\Madre\source\MadreView.mc" 262 16 ]
        %tmp.67 = self;
        symbol [ drawArc %tmp.68 262 16 23 ];
        %tmp.68 = getv function %tmp.67 :drawArc;
        %tmp.69 = lgetv %dc;
        symbol [ dc %tmp.69 262 24 26 ];
        %tmp.70 = lgetv %centerX.4;
        symbol [ centerX %tmp.70 262 28 35 ];
        %tmp.71 = lgetv %centerY.5;
        symbol [ centerY %tmp.71 262 37 44 ];
        %tmp.72 = lgetv %radius.6;
        symbol [ radius %tmp.72 262 46 52 ];
        %tmp.73 = -90;
        %tmp.74 = 360;
        invoke %tmp.67 %tmp.68(%tmp.69, %tmp.70, %tmp.71, %tmp.72, %tmp.73, %tmp.74);
[ "D:\Melpa\Madre\source\MadreView.mc" 265 16 ]
        %tmp.75 = lgetv %dc;
        symbol [ dc %tmp.75 265 16 18 ];
        symbol [ setColor %tmp.76 265 19 27 ];
        %tmp.76 = getv function %tmp.75 :setColor;
        symbol [ _accentColor %tmp.78 265 28 40 ];
        %tmp.78 = getv ? :_accentColor;
        symbol [ Graphics %tmp.79 265 42 50 ];
        %tmp.79 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.80 265 51 68 ];
        %tmp.80 = getv %tmp.79 :COLOR_TRANSPARENT;
        invoke %tmp.75 %tmp.76(%tmp.78, %tmp.80);
[ "D:\Melpa\Madre\source\MadreView.mc" 266 16 ]
        %tmp.81 = self;
        symbol [ drawArc %tmp.82 266 16 23 ];
        %tmp.82 = getv function %tmp.81 :drawArc;
        %tmp.83 = lgetv %dc;
        symbol [ dc %tmp.83 266 24 26 ];
        %tmp.84 = lgetv %centerX.4;
        symbol [ centerX %tmp.84 266 28 35 ];
        %tmp.85 = lgetv %centerY.5;
        symbol [ centerY %tmp.85 266 37 44 ];
        %tmp.86 = lgetv %radius.6;
        symbol [ radius %tmp.86 266 46 52 ];
        %tmp.87 = lgetv %startAngle.7;
        symbol [ startAngle %tmp.87 266 54 64 ];
        %tmp.88 = lgetv %sweepAngle.8;
        symbol [ sweepAngle %tmp.88 266 66 76 ];
        symbol [ toNumber %tmp.89 266 77 85 ];
        %tmp.89 = getv function %tmp.88 :toNumber;
        %tmp.90 = invoke %tmp.88 %tmp.89();
        invoke %tmp.81 %tmp.82(%tmp.83, %tmp.84, %tmp.85, %tmp.86, %tmp.87, %tmp.90);
D_Melpa_Madre_source_MadreView_mc_244_42_267_12_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_244_12_267_12_if_end;
D_Melpa_Madre_source_MadreView_mc_244_12_267_12_if_end:
D_Melpa_Madre_source_MadreView_mc_242_67_268_8_stop:
        goto @D_Melpa_Madre_source_MadreView_mc_242_8_268_8_if_end;
D_Melpa_Madre_source_MadreView_mc_242_8_268_8_if_end:
D_Melpa_Madre_source_MadreView_mc_239_58_269_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 271; @symbol_functiondef = [drawArc,271,21,28]; @symbol_param<0> = [dc,271,29,31]; @symbol_param<0>_type<0> = [Graphics,271,35,43]; @symbol_param<0>_type<1> = [Dc,271,44,46]; @symbol_param<1> = [centerX,271,48,55]; @symbol_param<1>_type<0> = [Number,271,59,65]; @symbol_param<2> = [centerY,271,67,74]; @symbol_param<2>_type<0> = [Number,271,78,84]; @symbol_param<3> = [radius,271,86,92]; @symbol_param<3>_type<0> = [Number,271,96,102]; @symbol_param<4> = [startAngle,272,27,37]; @symbol_param<4>_type<0> = [Number,272,41,47]; @symbol_param<5> = [sweepAngle,272,49,59]; @symbol_param<5>_type<0> = [Number,272,63,69]; ]
    private
    function drawArc(dc as Graphics.Dc, centerX as Number, centerY as Number, radius as Number, startAngle as Number, sweepAngle as Number) as Void {
D_Melpa_Madre_source_MadreView_mc_272_79_287_4_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 273 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_272_79_287_4_start" "D_Melpa_Madre_source_MadreView_mc_272_79_287_4_stop" ]
        %segments.1 = local;
        symbol [ segments %segments.1 273 12 20 ];
        %tmp.1 = 36;
        lputv %segments.1 %tmp.1;
        symbol [ segments %segments.1 273 12 20 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 274 8 ]
        [ "D_Melpa_Madre_source_MadreView_mc_272_79_287_4_start" "D_Melpa_Madre_source_MadreView_mc_272_79_287_4_stop" ]
        %angleStep.2 = local;
        symbol [ angleStep %angleStep.2 274 12 21 ];
        %tmp.2 = lgetv %sweepAngle;
        symbol [ sweepAngle %tmp.2 274 24 34 ];
        %tmp.3 = lgetv %segments.1;
        symbol [ segments %tmp.3 274 37 45 ];
        %tmp.4 = div %tmp.2 %tmp.3;
        lputv %angleStep.2 %tmp.4;
        symbol [ angleStep %angleStep.2 274 12 21 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 276 8 ]
        for @D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_test @D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_incr @D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_end;
D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_init:
        [ "D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_begin" "D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_end" ]
        %i.3 = local;
        symbol [ i %i.3 276 17 18 ];
        %tmp.5 = 0;
        lputv %i.3 %tmp.5;
        symbol [ i %i.3 276 17 18 ];
D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_test:
        %tmp.6 = lgetv %i.3;
        symbol [ i %tmp.6 276 24 25 ];
        %tmp.7 = lgetv %segments.1;
        symbol [ segments %tmp.7 276 28 36 ];
        %tmp.8 = lt %tmp.6 %tmp.7;
        bf %tmp.8 @D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_end;
D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_begin:
D_Melpa_Madre_source_MadreView_mc_276_43_286_8_start:
[ "D:\Melpa\Madre\source\MadreView.mc" 277 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_start" "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_stop" ]
        %angle1.4 = local;
        symbol [ angle1 %angle1.4 277 16 22 ];
        symbol [ Math %tmp.9 277 25 29 ];
        %tmp.9 = getm $.Toybox.Math;
        symbol [ toRadians %tmp.10 277 30 39 ];
        %tmp.10 = getv function %tmp.9 :toRadians;
        %tmp.11 = lgetv %startAngle;
        symbol [ startAngle %tmp.11 277 40 50 ];
        %tmp.12 = lgetv %i.3;
        symbol [ i %tmp.12 277 53 54 ];
        %tmp.13 = lgetv %angleStep.2;
        symbol [ angleStep %tmp.13 277 57 66 ];
        %tmp.14 = mul %tmp.12 %tmp.13;
        %tmp.15 = add %tmp.11 %tmp.14;
        %tmp.16 = invoke %tmp.9 %tmp.10(%tmp.15);
        lputv %angle1.4 %tmp.16;
        symbol [ angle1 %angle1.4 277 16 22 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 278 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_start" "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_stop" ]
        %angle2.5 = local;
        symbol [ angle2 %angle2.5 278 16 22 ];
        symbol [ Math %tmp.17 278 25 29 ];
        %tmp.17 = getm $.Toybox.Math;
        symbol [ toRadians %tmp.18 278 30 39 ];
        %tmp.18 = getv function %tmp.17 :toRadians;
        %tmp.19 = lgetv %startAngle;
        symbol [ startAngle %tmp.19 278 40 50 ];
        %tmp.20 = lgetv %i.3;
        symbol [ i %tmp.20 278 54 55 ];
        %tmp.21 = 1;
        %tmp.22 = add %tmp.20 %tmp.21;
        %tmp.23 = lgetv %angleStep.2;
        symbol [ angleStep %tmp.23 278 63 72 ];
        %tmp.24 = mul %tmp.22 %tmp.23;
        %tmp.25 = add %tmp.19 %tmp.24;
        %tmp.26 = invoke %tmp.17 %tmp.18(%tmp.25);
        lputv %angle2.5 %tmp.26;
        symbol [ angle2 %angle2.5 278 16 22 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 280 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_start" "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_stop" ]
        %x1.6 = local;
        symbol [ x1 %x1.6 280 16 18 ];
        %tmp.27 = lgetv %centerX;
        symbol [ centerX %tmp.27 280 21 28 ];
        %tmp.28 = lgetv %radius;
        symbol [ radius %tmp.28 280 31 37 ];
        symbol [ Math %tmp.29 280 40 44 ];
        %tmp.29 = getm $.Toybox.Math;
        symbol [ cos %tmp.30 280 45 48 ];
        %tmp.30 = getv function %tmp.29 :cos;
        %tmp.31 = lgetv %angle1.4;
        symbol [ angle1 %tmp.31 280 49 55 ];
        %tmp.32 = invoke %tmp.29 %tmp.30(%tmp.31);
        %tmp.33 = mul %tmp.28 %tmp.32;
        %tmp.34 = add %tmp.27 %tmp.33;
        lputv %x1.6 %tmp.34;
        symbol [ x1 %x1.6 280 16 18 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 281 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_start" "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_stop" ]
        %y1.7 = local;
        symbol [ y1 %y1.7 281 16 18 ];
        %tmp.35 = lgetv %centerY;
        symbol [ centerY %tmp.35 281 21 28 ];
        %tmp.36 = lgetv %radius;
        symbol [ radius %tmp.36 281 31 37 ];
        symbol [ Math %tmp.37 281 40 44 ];
        %tmp.37 = getm $.Toybox.Math;
        symbol [ sin %tmp.38 281 45 48 ];
        %tmp.38 = getv function %tmp.37 :sin;
        %tmp.39 = lgetv %angle1.4;
        symbol [ angle1 %tmp.39 281 49 55 ];
        %tmp.40 = invoke %tmp.37 %tmp.38(%tmp.39);
        %tmp.41 = mul %tmp.36 %tmp.40;
        %tmp.42 = add %tmp.35 %tmp.41;
        lputv %y1.7 %tmp.42;
        symbol [ y1 %y1.7 281 16 18 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 282 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_start" "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_stop" ]
        %x2.8 = local;
        symbol [ x2 %x2.8 282 16 18 ];
        %tmp.43 = lgetv %centerX;
        symbol [ centerX %tmp.43 282 21 28 ];
        %tmp.44 = lgetv %radius;
        symbol [ radius %tmp.44 282 31 37 ];
        symbol [ Math %tmp.45 282 40 44 ];
        %tmp.45 = getm $.Toybox.Math;
        symbol [ cos %tmp.46 282 45 48 ];
        %tmp.46 = getv function %tmp.45 :cos;
        %tmp.47 = lgetv %angle2.5;
        symbol [ angle2 %tmp.47 282 49 55 ];
        %tmp.48 = invoke %tmp.45 %tmp.46(%tmp.47);
        %tmp.49 = mul %tmp.44 %tmp.48;
        %tmp.50 = add %tmp.43 %tmp.49;
        lputv %x2.8 %tmp.50;
        symbol [ x2 %x2.8 282 16 18 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 283 12 ]
        [ "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_start" "D_Melpa_Madre_source_MadreView_mc_276_43_286_8_stop" ]
        %y2.9 = local;
        symbol [ y2 %y2.9 283 16 18 ];
        %tmp.51 = lgetv %centerY;
        symbol [ centerY %tmp.51 283 21 28 ];
        %tmp.52 = lgetv %radius;
        symbol [ radius %tmp.52 283 31 37 ];
        symbol [ Math %tmp.53 283 40 44 ];
        %tmp.53 = getm $.Toybox.Math;
        symbol [ sin %tmp.54 283 45 48 ];
        %tmp.54 = getv function %tmp.53 :sin;
        %tmp.55 = lgetv %angle2.5;
        symbol [ angle2 %tmp.55 283 49 55 ];
        %tmp.56 = invoke %tmp.53 %tmp.54(%tmp.55);
        %tmp.57 = mul %tmp.52 %tmp.56;
        %tmp.58 = add %tmp.51 %tmp.57;
        lputv %y2.9 %tmp.58;
        symbol [ y2 %y2.9 283 16 18 ];
[ "D:\Melpa\Madre\source\MadreView.mc" 285 12 ]
        %tmp.59 = lgetv %dc;
        symbol [ dc %tmp.59 285 12 14 ];
        symbol [ drawLine %tmp.60 285 15 23 ];
        %tmp.60 = getv function %tmp.59 :drawLine;
        %tmp.61 = lgetv %x1.6;
        symbol [ x1 %tmp.61 285 24 26 ];
        %tmp.62 = lgetv %y1.7;
        symbol [ y1 %tmp.62 285 28 30 ];
        %tmp.63 = lgetv %x2.8;
        symbol [ x2 %tmp.63 285 32 34 ];
        %tmp.64 = lgetv %y2.9;
        symbol [ y2 %tmp.64 285 36 38 ];
        invoke %tmp.59 %tmp.60(%tmp.61, %tmp.62, %tmp.63, %tmp.64);
D_Melpa_Madre_source_MadreView_mc_276_43_286_8_stop:
D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_incr:
[ "D:\Melpa\Madre\source\MadreView.mc" 276 ]
        %tmp.67 = lgetv %i.3;
        symbol [ i %tmp.67 276 38 39 ];
        %tmp.68 = add %tmp.67 1;
        lputv %i.3 %tmp.68;
        symbol [ i %i.3 276 38 39 ];
        goto @D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_test;
D_Melpa_Madre_source_MadreView_mc_276_8_286_8_for_end:
D_Melpa_Madre_source_MadreView_mc_272_79_287_4_stop:
    }
}
[ @file = "D:\Melpa\Madre\source\MadreView.mc"; @line = 1; ]
<init> {
}
