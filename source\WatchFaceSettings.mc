import Toybox.Application.Storage;
import Toybox.Graphics;
import Toybox.WatchUi;

class WatchFaceSettingsMenu extends WatchUi.Menu2 {

    function initialize() {
        Menu2.initialize({:title=>"Settings"});

        Menu2.addItem(new WatchUi.MenuItem(
            "Theme",
            "Light/Dark",
            "theme",
            null
        ));

        Menu2.addItem(new WatchUi.MenuItem(
            "Show Seconds",
            "On/Off",
            "seconds",
            null
        ));

        Menu2.addItem(new WatchUi.MenuItem(
            "Data Fields",
            "Customize",
            "datafields",
            null
        ));
    }
}

class WatchFaceSettingsDelegate extends WatchUi.Menu2InputDelegate {

    function initialize() {
        Menu2InputDelegate.initialize();
    }

    function onSelect(item as WatchUi.MenuItem) as Void {
        var id = item.getId();

        if (id.equals("theme")) {
            toggleTheme();
        } else if (id.equals("seconds")) {
            toggleSeconds();
        } else if (id.equals("datafields")) {
            customizeDataFields();
        }

        WatchUi.popView(WatchUi.SLIDE_IMMEDIATE);
    }

    private function toggleTheme() as Void {
        var currentTheme = Storage.getValue("theme");
        if (currentTheme == null) {
            currentTheme = "dark";
        }

        var newTheme = currentTheme.equals("dark") ? "light" : "dark";
        Storage.setValue("theme", newTheme);
    }

    private function toggleSeconds() as Void {
        var showSeconds = Storage.getValue("showSeconds");
        if (showSeconds == null) {
            showSeconds = false;
        }

        Storage.setValue("showSeconds", !showSeconds);
    }

    private function customizeDataFields() as Void {
        // Implementation for data field customization
        // This would typically open another menu or dialog
    }
}
