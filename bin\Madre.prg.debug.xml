<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<debugInfo>
    <manifest filename="D:\Melpa\Madre\manifest.xml"/>
    <pcToLineNum>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="1" lineNum="5" parent="globals/WatchFaceSettingsMenu" pc="268435460" symbol="&lt;init&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="2" lineNum="31" parent="globals" pc="268435472" symbol="getApp"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="2" lineNum="32" parent="globals" pc="268435482" symbol="getApp"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="3" lineNum="6" parent="globals/Background" pc="268435493" symbol="&lt;init&gt;"/>
        <entry filename="d:\\Melpa\\Madre\\bin\\gen\\006-B4587-00\\source\\Rez.mcgen" id="4" lineNum="7" parent="globals/Rez" pc="268435505" symbol="&lt;init&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="5" lineNum="5" parent="globals/MadreApp" pc="268435525" symbol="&lt;init&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="6" lineNum="11" parent="globals/MadreView" pc="268435537" symbol="&lt;init&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="7" lineNum="33" parent="globals/WatchFaceSettingsDelegate" pc="268435549" symbol="&lt;init&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="8" lineNum="7" parent="globals/WatchFaceSettingsMenu" pc="268435561" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="8" lineNum="8" parent="globals/WatchFaceSettingsMenu" pc="268435563" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="8" lineNum="10" parent="globals/WatchFaceSettingsMenu" pc="268435593" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="8" lineNum="17" parent="globals/WatchFaceSettingsMenu" pc="268435645" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="8" lineNum="24" parent="globals/WatchFaceSettingsMenu" pc="268435697" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="9" lineNum="8" parent="globals/Background" pc="268435750" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="9" lineNum="9" parent="globals/Background" pc="268435753" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="9" lineNum="13" parent="globals/Background" pc="268435771" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="16" parent="globals/Background" pc="268435788" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="18" parent="globals/Background" pc="268435791" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="21" parent="globals/Background" pc="268435794" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="22" parent="globals/Background" pc="268435798" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="23" parent="globals/Background" pc="268435817" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="24" parent="globals/Background" pc="268435823" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="28" parent="globals/Background" pc="268435833" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="29" parent="globals/Background" pc="268435837" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="30" parent="globals/Background" pc="268435856" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="31" parent="globals/Background" pc="268435862" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="36" parent="globals/Background" pc="268435872" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreBackground.mc" id="10" lineNum="37" parent="globals/Background" pc="268435886" symbol="draw"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="11" lineNum="12" parent="globals/MadreApp" pc="268435895" symbol="onStart"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="12" lineNum="25" parent="globals/MadreApp" pc="268435898" symbol="onSettingsChanged"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="12" lineNum="26" parent="globals/MadreApp" pc="268435900" symbol="onSettingsChanged"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="13" lineNum="20" parent="globals/MadreApp" pc="268435912" symbol="getInitialView"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="13" lineNum="21" parent="globals/MadreApp" pc="268435914" symbol="getInitialView"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="14" lineNum="7" parent="globals/MadreApp" pc="268435944" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="14" lineNum="8" parent="globals/MadreApp" pc="268435946" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreApp.mc" id="15" lineNum="16" parent="globals/MadreApp" pc="268435959" symbol="onStop"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="127" parent="globals/MadreView" pc="268435962" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="129" parent="globals/MadreView" pc="268435965" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="131" parent="globals/MadreView" pc="268435982" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="132" parent="globals/MadreView" pc="268435987" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="137" parent="globals/MadreView" pc="268436057" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="138" parent="globals/MadreView" pc="268436065" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="139" parent="globals/MadreView" pc="268436073" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="140" parent="globals/MadreView" pc="268436083" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="141" parent="globals/MadreView" pc="268436090" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="143" parent="globals/MadreView" pc="268436097" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="149" parent="globals/MadreView" pc="268436159" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="150" parent="globals/MadreView" pc="268436176" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="151" parent="globals/MadreView" pc="268436194" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="152" parent="globals/MadreView" pc="268436209" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="16" lineNum="154" parent="globals/MadreView" pc="268436228" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="170" parent="globals/MadreView" pc="268436252" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="171" parent="globals/MadreView" pc="268436255" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="172" parent="globals/MadreView" pc="268436267" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="175" parent="globals/MadreView" pc="268436271" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="176" parent="globals/MadreView" pc="268436293" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="177" parent="globals/MadreView" pc="268436308" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="178" parent="globals/MadreView" pc="268436325" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="181" parent="globals/MadreView" pc="268436353" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="182" parent="globals/MadreView" pc="268436370" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="186" parent="globals/MadreView" pc="268436404" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="187" parent="globals/MadreView" pc="268436426" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="188" parent="globals/MadreView" pc="268436441" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="189" parent="globals/MadreView" pc="268436458" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="192" parent="globals/MadreView" pc="268436489" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="17" lineNum="193" parent="globals/MadreView" pc="268436506" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="239" parent="globals/MadreView" pc="268436544" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="240" parent="globals/MadreView" pc="268436547" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="242" parent="globals/MadreView" pc="268436559" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="243" parent="globals/MadreView" pc="268436581" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="244" parent="globals/MadreView" pc="268436590" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="245" parent="globals/MadreView" pc="268436603" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="246" parent="globals/MadreView" pc="268436627" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="247" parent="globals/MadreView" pc="268436638" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="251" parent="globals/MadreView" pc="268436648" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="252" parent="globals/MadreView" pc="268436657" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="253" parent="globals/MadreView" pc="268436667" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="254" parent="globals/MadreView" pc="268436671" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="255" parent="globals/MadreView" pc="268436675" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="257" parent="globals/MadreView" pc="268436683" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="258" parent="globals/MadreView" pc="268436700" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="261" parent="globals/MadreView" pc="268436712" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="262" parent="globals/MadreView" pc="268436728" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="265" parent="globals/MadreView" pc="268436750" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="18" lineNum="266" parent="globals/MadreView" pc="268436767" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="225" parent="globals/MadreView" pc="268436800" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="226" parent="globals/MadreView" pc="268436803" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="228" parent="globals/MadreView" pc="268436815" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="229" parent="globals/MadreView" pc="268436837" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="230" parent="globals/MadreView" pc="268436852" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="231" parent="globals/MadreView" pc="268436869" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="234" parent="globals/MadreView" pc="268436900" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="20" lineNum="235" parent="globals/MadreView" pc="268436917" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="21" lineNum="116" parent="globals/MadreView" pc="268436955" symbol="onExitSleep"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="21" lineNum="117" parent="globals/MadreView" pc="268436957" symbol="onExitSleep"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="21" lineNum="118" parent="globals/MadreView" pc="268436965" symbol="onExitSleep"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="37" parent="globals/MadreView" pc="268436977" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="38" parent="globals/MadreView" pc="268436980" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="39" parent="globals/MadreView" pc="268436994" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="40" parent="globals/MadreView" pc="268437008" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="43" parent="globals/MadreView" pc="268437041" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="44" parent="globals/MadreView" pc="268437053" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="45" parent="globals/MadreView" pc="268437062" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="46" parent="globals/MadreView" pc="268437070" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="47" parent="globals/MadreView" pc="268437082" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="49" parent="globals/MadreView" pc="268437096" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="50" parent="globals/MadreView" pc="268437104" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="51" parent="globals/MadreView" pc="268437116" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="55" parent="globals/MadreView" pc="268437127" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="56" parent="globals/MadreView" pc="268437170" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="23" lineNum="57" parent="globals/MadreView" pc="268437213" symbol="onLayout"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="271" parent="globals/MadreView" pc="268437257" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="273" parent="globals/MadreView" pc="268437260" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="274" parent="globals/MadreView" pc="268437264" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="276" parent="globals/MadreView" pc="268437271" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="277" parent="globals/MadreView" pc="268437282" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="278" parent="globals/MadreView" pc="268437304" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="280" parent="globals/MadreView" pc="268437329" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="281" parent="globals/MadreView" pc="268437351" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="282" parent="globals/MadreView" pc="268437373" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="283" parent="globals/MadreView" pc="268437395" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="24" lineNum="285" parent="globals/MadreView" pc="268437417" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="25" lineNum="122" parent="globals/MadreView" pc="268437446" symbol="onEnterSleep"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="25" lineNum="123" parent="globals/MadreView" pc="268437448" symbol="onEnterSleep"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="25" lineNum="124" parent="globals/MadreView" pc="268437456" symbol="onEnterSleep"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="197" parent="globals/MadreView" pc="268437468" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="198" parent="globals/MadreView" pc="268437471" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="199" parent="globals/MadreView" pc="268437488" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="201" parent="globals/MadreView" pc="268437528" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="202" parent="globals/MadreView" pc="268437545" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="205" parent="globals/MadreView" pc="268437568" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="206" parent="globals/MadreView" pc="268437585" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="207" parent="globals/MadreView" pc="268437589" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="208" parent="globals/MadreView" pc="268437593" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="209" parent="globals/MadreView" pc="268437597" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="212" parent="globals/MadreView" pc="268437601" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="213" parent="globals/MadreView" pc="268437619" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="216" parent="globals/MadreView" pc="268437646" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="217" parent="globals/MadreView" pc="268437659" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="218" parent="globals/MadreView" pc="268437667" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="220" parent="globals/MadreView" pc="268437686" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="26" lineNum="222" parent="globals/MadreView" pc="268437703" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="27" lineNum="30" parent="globals/MadreView" pc="268437731" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="27" lineNum="31" parent="globals/MadreView" pc="268437733" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="27" lineNum="32" parent="globals/MadreView" pc="268437745" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="27" lineNum="33" parent="globals/MadreView" pc="268437753" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="67" parent="globals/MadreView" pc="268437762" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="68" parent="globals/MadreView" pc="268437765" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="70" parent="globals/MadreView" pc="268437768" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="71" parent="globals/MadreView" pc="268437776" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="73" parent="globals/MadreView" pc="268437783" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="76" parent="globals/MadreView" pc="268437787" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="77" parent="globals/MadreView" pc="268437799" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="80" parent="globals/MadreView" pc="268437825" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="81" parent="globals/MadreView" pc="268437845" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="84" parent="globals/MadreView" pc="268437853" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="87" parent="globals/MadreView" pc="268437866" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="90" parent="globals/MadreView" pc="268437879" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="92" parent="globals/MadreView" pc="268437887" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="95" parent="globals/MadreView" pc="268437898" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="98" parent="globals/MadreView" pc="268437909" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="101" parent="globals/MadreView" pc="268437920" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="104" parent="globals/MadreView" pc="268437934" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="28" lineNum="105" parent="globals/MadreView" pc="268437942" symbol="onUpdate"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="29" lineNum="157" parent="globals/MadreView" pc="268437957" symbol="&lt;globals/MadreView/&lt;&gt;drawDate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="29" lineNum="158" parent="globals/MadreView" pc="268437960" symbol="&lt;globals/MadreView/&lt;&gt;drawDate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="29" lineNum="163" parent="globals/MadreView" pc="268438007" symbol="&lt;globals/MadreView/&lt;&gt;drawDate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="29" lineNum="164" parent="globals/MadreView" pc="268438024" symbol="&lt;globals/MadreView/&lt;&gt;drawDate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="29" lineNum="165" parent="globals/MadreView" pc="268438033" symbol="&lt;globals/MadreView/&lt;&gt;drawDate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\MadreView.mc" id="29" lineNum="167" parent="globals/MadreView" pc="268438046" symbol="&lt;globals/MadreView/&lt;&gt;drawDate&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="30" lineNum="53" parent="globals/WatchFaceSettingsDelegate" pc="268438070" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="30" lineNum="54" parent="globals/WatchFaceSettingsDelegate" pc="268438073" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="30" lineNum="55" parent="globals/WatchFaceSettingsDelegate" pc="268438092" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="30" lineNum="56" parent="globals/WatchFaceSettingsDelegate" pc="268438098" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="30" lineNum="59" parent="globals/WatchFaceSettingsDelegate" pc="268438108" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="30" lineNum="60" parent="globals/WatchFaceSettingsDelegate" pc="268438140" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="31" lineNum="63" parent="globals/WatchFaceSettingsDelegate" pc="268438161" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleSeconds&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="31" lineNum="64" parent="globals/WatchFaceSettingsDelegate" pc="268438164" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleSeconds&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="31" lineNum="65" parent="globals/WatchFaceSettingsDelegate" pc="268438183" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleSeconds&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="31" lineNum="66" parent="globals/WatchFaceSettingsDelegate" pc="268438189" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleSeconds&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="31" lineNum="69" parent="globals/WatchFaceSettingsDelegate" pc="268438195" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleSeconds&gt;"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="32" lineNum="35" parent="globals/WatchFaceSettingsDelegate" pc="268438217" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="32" lineNum="36" parent="globals/WatchFaceSettingsDelegate" pc="268438219" symbol="initialize"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="39" parent="globals/WatchFaceSettingsDelegate" pc="268438232" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="40" parent="globals/WatchFaceSettingsDelegate" pc="268438235" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="42" parent="globals/WatchFaceSettingsDelegate" pc="268438244" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="43" parent="globals/WatchFaceSettingsDelegate" pc="268438261" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="44" parent="globals/WatchFaceSettingsDelegate" pc="268438271" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="45" parent="globals/WatchFaceSettingsDelegate" pc="268438288" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="46" parent="globals/WatchFaceSettingsDelegate" pc="268438298" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="47" parent="globals/WatchFaceSettingsDelegate" pc="268438315" symbol="onSelect"/>
        <entry filename="D:\\Melpa\\Madre\\source\\WatchFaceSettings.mc" id="33" lineNum="50" parent="globals/WatchFaceSettingsDelegate" pc="268438325" symbol="onSelect"/>
        <entry filename="d:\\Melpa\\Madre\\bin\\gen\\006-B4587-00\\source\\Rez.mcgen" id="35" lineNum="13" parent="globals/Rez/Layouts" pc="268438340" symbol="WatchFace"/>
        <entry filename="d:\\Melpa\\Madre\\bin\\gen\\006-B4587-00\\source\\Rez.mcgen" id="35" lineNum="14" parent="globals/Rez/Layouts" pc="268438351" symbol="WatchFace"/>
        <entry filename="d:\\Melpa\\Madre\\bin\\gen\\006-B4587-00\\source\\Rez.mcgen" id="35" lineNum="16" parent="globals/Rez/Layouts" pc="268438375" symbol="WatchFace"/>
        <entry filename="d:\\Melpa\\Madre\\bin\\gen\\006-B4587-00\\source\\Rez.mcgen" id="35" lineNum="18" parent="globals/Rez/Layouts" pc="268438479" symbol="WatchFace"/>
    </pcToLineNum>
    <symbolTable>
        <entry id="8388639" module="true" symbol="Graphics"/>
        <entry id="8388632" method="true" symbol="&lt;init&gt;"/>
        <entry id="8389683" method="true" symbol="onSettingsChanged"/>
        <entry field="true" id="8388737" symbol="calories"/>
        <entry field="true" id="39" symbol="ColorRed"/>
        <entry field="true" id="20" symbol="&lt;globals/MadreView/&lt;&gt;_smallFont&gt;"/>
        <entry id="8390424" module="true" symbol="Storage"/>
        <entry field="true" id="42" symbol="MilitaryFormatTitle"/>
        <entry id="8388924" module="true" symbol="Gregorian"/>
        <entry id="8388647" module="true" symbol="UserProfile"/>
        <entry field="true" id="8388611" module="true" symbol="Toybox"/>
        <entry field="true" id="36" symbol="ColorBlue"/>
        <entry field="true" id="8388782" method="true" symbol="size"/>
        <entry field="true" id="33" symbol="AppName"/>
        <entry id="24" method="true" symbol="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;"/>
        <entry id="8388633" module="true" symbol="ActivityMonitor"/>
        <entry id="5" module="true" symbol="globals/Rez/Strings"/>
        <entry id="28" method="true" symbol="&lt;globals/MadreView/&lt;&gt;drawTime&gt;"/>
        <entry field="true" id="8388912" symbol="steps"/>
        <entry id="8388648" module="true" symbol="WatchUi"/>
        <entry id="2" module="true" symbol="globals/Rez"/>
        <entry id="8389124" method="true" symbol="onUpdate"/>
        <entry id="8389375" method="true" object="true" symbol="WatchFace"/>
        <entry field="true" id="8389065" symbol="locY"/>
        <entry field="true" id="8389064" symbol="locX"/>
        <entry id="25" method="true" symbol="&lt;globals/MadreView/&lt;&gt;drawDate&gt;"/>
        <entry id="22" method="true" symbol="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;"/>
        <entry field="true" id="19" symbol="&lt;globals/MadreView/&lt;&gt;_screenWidth&gt;"/>
        <entry id="9" object="true" symbol="WatchFaceSettingsMenu"/>
        <entry id="8388771" module="true" object="true" symbol="Drawables"/>
        <entry id="8389348" symbol="font"/>
        <entry id="8389275" method="true" symbol="getApp"/>
        <entry field="true" id="37" symbol="ColorDarkGray"/>
        <entry id="4" module="true" symbol="globals/Rez/Layouts"/>
        <entry id="8389122" method="true" symbol="onLayout"/>
        <entry id="8389376" method="true" symbol="onEnterSleep"/>
        <entry field="true" id="8389063" symbol="identifier"/>
        <entry id="27" method="true" symbol="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;"/>
        <entry id="8389349" symbol="justification"/>
        <entry id="6" object="true" symbol="MadreApp"/>
        <entry id="31" method="true" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;"/>
        <entry field="true" id="14" symbol="&lt;globals/MadreView/&lt;&gt;_foregroundColor&gt;"/>
        <entry id="23" method="true" symbol="&lt;globals/MadreView/&lt;&gt;drawArc&gt;"/>
        <entry field="true" id="40" symbol="ColorWhite"/>
        <entry field="true" id="11" symbol="&lt;globals/MadreView/&lt;&gt;_accentColor&gt;"/>
        <entry field="true" id="35" symbol="ColorBlack"/>
        <entry id="8" object="true" symbol="WatchFaceSettingsDelegate"/>
        <entry id="3" module="true" symbol="globals/Rez/Drawables"/>
        <entry id="26" method="true" symbol="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;"/>
        <entry id="17" symbol="&lt;globals/MadreView/&lt;&gt;_screenCenterPoint&gt;"/>
        <entry id="8389377" method="true" symbol="onExitSleep"/>
        <entry id="8388644" module="true" symbol="System"/>
        <entry id="8389619" method="true" symbol="onSelect"/>
        <entry id="8388645" module="true" symbol="Time"/>
        <entry id="8388769" module="true" object="true" symbol="Rez"/>
        <entry id="7" object="true" symbol="MadreView"/>
        <entry id="8392219" symbol="face"/>
        <entry id="8388640" module="true" symbol="Lang"/>
        <entry id="8389068" method="true" symbol="draw"/>
        <entry field="true" id="32" symbol="LauncherIcon"/>
        <entry field="true" id="16" symbol="&lt;globals/MadreView/&lt;&gt;_partialUpdatesAllowed&gt;"/>
        <entry id="8388610" symbol="statics"/>
        <entry id="8389123" method="true" symbol="onShow"/>
        <entry id="8389350" module="true" symbol="globals"/>
        <entry field="true" id="34" symbol="BackgroundColorTitle"/>
        <entry id="8388770" module="true" object="true" symbol="Strings"/>
        <entry field="true" id="21" symbol="&lt;globals/MadreView/&lt;&gt;_timeFont&gt;"/>
        <entry id="8390146" module="true" object="true" symbol="Background"/>
        <entry id="8388702" method="true" symbol="initialize"/>
        <entry id="29" method="true" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;customizeDataFields&gt;"/>
        <entry id="8389279" method="true" symbol="onStart"/>
        <entry field="true" id="13" symbol="&lt;globals/MadreView/&lt;&gt;_dataFont&gt;"/>
        <entry id="8388641" module="true" symbol="Math"/>
        <entry field="true" id="8388747" symbol="currentHeartRate"/>
        <entry field="true" id="38" symbol="ColorLightGray"/>
        <entry id="8389280" method="true" symbol="getInitialView"/>
        <entry id="8389281" method="true" symbol="onStop"/>
        <entry field="true" id="8389347" symbol="color"/>
        <entry field="true" id="41" symbol="ForegroundColorTitle"/>
        <entry field="true" id="18" symbol="&lt;globals/MadreView/&lt;&gt;_screenHeight&gt;"/>
        <entry id="10" module="true" symbol="Layouts"/>
        <entry field="true" id="12" symbol="&lt;globals/MadreView/&lt;&gt;_backgroundColor&gt;"/>
        <entry field="true" id="15" symbol="&lt;globals/MadreView/&lt;&gt;_isAwake&gt;"/>
        <entry id="8389125" method="true" symbol="onHide"/>
        <entry id="30" method="true" symbol="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleSeconds&gt;"/>
        <entry id="8388635" module="true" symbol="Application"/>
        <entry field="true" id="8389642" symbol="title"/>
    </symbolTable>
    <localVars>
        <entry endPc="268435786" name="dictionary" stackId="1" startPc="268435753"/>
        <entry arg="true" endPc="268435894" name="dc" stackId="1" startPc="268435788"/>
        <entry endPc="268435893" name="backgroundColor" stackId="2" startPc="268435791"/>
        <entry endPc="268435829" name="bgColor" stackId="3" startPc="268435798"/>
        <entry endPc="268435868" name="bgColor" stackId="4" startPc="268435837"/>
        <entry arg="true" endPc="268436251" name="dc" stackId="1" startPc="268435962"/>
        <entry arg="true" endPc="268436251" name="clockTime" stackId="2" startPc="268435962"/>
        <entry endPc="268436250" name="timeString" stackId="3" startPc="268435965"/>
        <entry endPc="268436250" name="is24Hour" stackId="4" startPc="268435965"/>
        <entry endPc="268436158" name="hour" stackId="5" startPc="268436057"/>
        <entry endPc="268436250" name="timeTextDimensions" stackId="6" startPc="268435965"/>
        <entry endPc="268436250" name="timeX" stackId="7" startPc="268435965"/>
        <entry endPc="268436250" name="timeY" stackId="8" startPc="268435965"/>
        <entry arg="true" endPc="268436543" name="dc" stackId="1" startPc="268436252"/>
        <entry endPc="268436542" name="activityInfo" stackId="2" startPc="268436255"/>
        <entry endPc="268436542" name="yOffset" stackId="3" startPc="268436255"/>
        <entry endPc="268436400" name="stepsString" stackId="4" startPc="268436293"/>
        <entry endPc="268436539" name="caloriesString" stackId="5" startPc="268436426"/>
        <entry arg="true" endPc="268436799" name="dc" stackId="1" startPc="268436544"/>
        <entry endPc="268436798" name="activityInfo" stackId="2" startPc="268436547"/>
        <entry endPc="268436795" name="goal" stackId="3" startPc="268436581"/>
        <entry endPc="268436792" name="progress" stackId="4" startPc="268436603"/>
        <entry endPc="268436792" name="centerX" stackId="5" startPc="268436603"/>
        <entry endPc="268436792" name="centerY" stackId="6" startPc="268436603"/>
        <entry endPc="268436792" name="radius" stackId="7" startPc="268436603"/>
        <entry endPc="268436792" name="startAngle" stackId="8" startPc="268436603"/>
        <entry endPc="268436792" name="sweepAngle" stackId="9" startPc="268436603"/>
        <entry arg="true" endPc="268436954" name="dc" stackId="1" startPc="268436800"/>
        <entry endPc="268436953" name="activityInfo" stackId="2" startPc="268436803"/>
        <entry endPc="268436950" name="hrString" stackId="3" startPc="268436837"/>
        <entry arg="true" endPc="268437256" name="dc" stackId="1" startPc="268436977"/>
        <entry endPc="268437255" name="deviceSettings" stackId="2" startPc="268436980"/>
        <entry arg="true" endPc="268437445" name="dc" stackId="1" startPc="268437257"/>
        <entry arg="true" endPc="268437445" name="centerX" stackId="2" startPc="268437257"/>
        <entry arg="true" endPc="268437445" name="centerY" stackId="3" startPc="268437257"/>
        <entry arg="true" endPc="268437445" name="radius" stackId="4" startPc="268437257"/>
        <entry arg="true" endPc="268437445" name="startAngle" stackId="5" startPc="268437257"/>
        <entry arg="true" endPc="268437445" name="sweepAngle" stackId="6" startPc="268437257"/>
        <entry endPc="268437444" name="segments" stackId="7" startPc="268437260"/>
        <entry endPc="268437444" name="angleStep" stackId="8" startPc="268437260"/>
        <entry endPc="268437444" name="i" stackId="9" startPc="268437282"/>
        <entry endPc="268437434" name="angle1" stackId="10" startPc="268437282"/>
        <entry endPc="268437434" name="angle2" stackId="11" startPc="268437282"/>
        <entry endPc="268437434" name="x1" stackId="12" startPc="268437282"/>
        <entry endPc="268437434" name="y1" stackId="13" startPc="268437282"/>
        <entry endPc="268437434" name="x2" stackId="14" startPc="268437282"/>
        <entry endPc="268437434" name="y2" stackId="15" startPc="268437282"/>
        <entry arg="true" endPc="268437730" name="dc" stackId="1" startPc="268437468"/>
        <entry endPc="268437729" name="battery" stackId="2" startPc="268437471"/>
        <entry endPc="268437729" name="batteryString" stackId="3" startPc="268437471"/>
        <entry endPc="268437729" name="batteryWidth" stackId="4" startPc="268437471"/>
        <entry endPc="268437729" name="batteryHeight" stackId="5" startPc="268437471"/>
        <entry endPc="268437729" name="batteryX" stackId="6" startPc="268437471"/>
        <entry endPc="268437729" name="batteryY" stackId="7" startPc="268437471"/>
        <entry endPc="268437729" name="fillWidth" stackId="8" startPc="268437471"/>
        <entry arg="true" endPc="268437956" name="dc" stackId="1" startPc="268437762"/>
        <entry endPc="268437955" name="targetDc" stackId="2" startPc="268437765"/>
        <entry endPc="268437955" name="clockTime" stackId="3" startPc="268437765"/>
        <entry endPc="268437955" name="date" stackId="4" startPc="268437765"/>
        <entry arg="true" endPc="268438069" name="dc" stackId="1" startPc="268437957"/>
        <entry arg="true" endPc="268438069" name="date" stackId="2" startPc="268437957"/>
        <entry endPc="268438068" name="dateString" stackId="3" startPc="268437960"/>
        <entry endPc="268438068" name="dateX" stackId="4" startPc="268437960"/>
        <entry endPc="268438068" name="dateY" stackId="5" startPc="268437960"/>
        <entry endPc="268438159" name="currentTheme" stackId="1" startPc="268438073"/>
        <entry endPc="268438159" name="newTheme" stackId="2" startPc="268438073"/>
        <entry endPc="268438215" name="showSeconds" stackId="1" startPc="268438164"/>
        <entry arg="true" endPc="268438339" name="item" stackId="1" startPc="268438232"/>
        <entry endPc="268438338" name="id" stackId="2" startPc="268438235"/>
        <entry arg="true" endPc="268438493" name="dc" stackId="1" startPc="268438340"/>
        <entry endPc="268438493" name="rez_cmp_local_custom_drawable_11939594661139614796" stackId="2" startPc="268438351"/>
        <entry endPc="268438493" name="rez_cmp_local_text_TimeLabel" stackId="3" startPc="268438351"/>
    </localVars>
    <deviceInfo deviceVersion="" partNumber="006-B4587-00"/>
    <annotations>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="MilitaryFormatTitle"/>
        <annotationEntry annotation="initialized" class="Drawables" module="globals/Rez" symbol="LauncherIcon"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="BackgroundColorTitle"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="ColorRed"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="ColorDarkGray"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="ColorWhite"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="ForegroundColorTitle"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="ColorLightGray"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="AppName"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="ColorBlue"/>
        <annotationEntry annotation="initialized" class="Strings" module="globals/Rez" symbol="ColorBlack"/>
    </annotations>
    <dataEntryOffsetMappings>
        <dataEntry label="globals" offset="58" parentId="" symbolId="globals" type="module"/>
        <dataEntry label="globals/WatchFaceSettingsMenu" offset="152" parentId="globals" symbolId="WatchFaceSettingsMenu" type="class"/>
        <dataEntry label="globals/Background" offset="196" parentId="globals" symbolId="Background" type="class"/>
        <dataEntry label="globals/Rez" offset="249" parentId="globals" symbolId="Rez" type="module"/>
        <dataEntry label="globals/MadreApp" offset="302" parentId="globals" symbolId="MadreApp" type="class"/>
        <dataEntry label="globals/MadreView" offset="382" parentId="globals" symbolId="MadreView" type="class"/>
        <dataEntry label="globals/WatchFaceSettingsDelegate" offset="642" parentId="globals" symbolId="WatchFaceSettingsDelegate" type="class"/>
        <dataEntry label="globals/Rez/Drawables" offset="722" parentId="Rez" symbolId="Drawables" type="module"/>
        <dataEntry label="globals/Rez/Layouts" offset="757" parentId="Rez" symbolId="Layouts" type="module"/>
        <dataEntry label="globals/Rez/Strings" offset="792" parentId="Rez" symbolId="Strings" type="module"/>
    </dataEntryOffsetMappings>
    <functions>
        <functionEntry accessMode="public" endPc="268435471" name="&lt;init&gt;" parent="WatchFaceSettingsMenu" startPc="268435460">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435492" name="getApp" startPc="268435472">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435504" name="&lt;init&gt;" parent="Background" startPc="268435493">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435524" name="&lt;init&gt;" parent="Rez" startPc="268435505">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435536" name="&lt;init&gt;" parent="MadreApp" startPc="268435525">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435548" name="&lt;init&gt;" parent="MadreView" startPc="268435537">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435560" name="&lt;init&gt;" parent="WatchFaceSettingsDelegate" startPc="268435549">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435749" name="initialize" parent="WatchFaceSettingsMenu" startPc="268435561">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435787" name="initialize" parent="Background" startPc="268435750">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435894" name="draw" parent="Background" startPc="268435788">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" name="&lt;init&gt;" parent="Drawables">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" name="&lt;init&gt;" parent="Layouts">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" name="&lt;init&gt;" parent="Strings">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435897" name="onStart" parent="MadreApp" startPc="268435895">
            <param id="state"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435911" name="onSettingsChanged" parent="MadreApp" startPc="268435898">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435943" name="getInitialView" parent="MadreApp" startPc="268435912">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435958" name="initialize" parent="MadreApp" startPc="268435944">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268435961" name="onStop" parent="MadreApp" startPc="268435959">
            <param id="state"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268436251" name="&lt;globals/MadreView/&lt;&gt;drawTime&gt;" parent="MadreView" startPc="268435962">
            <param id="dc"/>
            <param id="clockTime"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268436543" name="&lt;globals/MadreView/&lt;&gt;drawActivityData&gt;" parent="MadreView" startPc="268436252">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268436799" name="&lt;globals/MadreView/&lt;&gt;drawSteps&gt;" parent="MadreView" startPc="268436544">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" name="onHide" parent="MadreView">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268436954" name="&lt;globals/MadreView/&lt;&gt;drawHeartRate&gt;" parent="MadreView" startPc="268436800">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268436976" name="onExitSleep" parent="MadreView" startPc="268436955">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" name="onShow" parent="MadreView">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268437256" name="onLayout" parent="MadreView" startPc="268436977">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268437445" name="&lt;globals/MadreView/&lt;&gt;drawArc&gt;" parent="MadreView" startPc="268437257">
            <param id="dc"/>
            <param id="centerX"/>
            <param id="centerY"/>
            <param id="radius"/>
            <param id="startAngle"/>
            <param id="sweepAngle"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268437467" name="onEnterSleep" parent="MadreView" startPc="268437446">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268437730" name="&lt;globals/MadreView/&lt;&gt;drawBattery&gt;" parent="MadreView" startPc="268437468">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268437761" name="initialize" parent="MadreView" startPc="268437731">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268437956" name="onUpdate" parent="MadreView" startPc="268437762">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268438069" name="&lt;globals/MadreView/&lt;&gt;drawDate&gt;" parent="MadreView" startPc="268437957">
            <param id="dc"/>
            <param id="date"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268438160" name="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleTheme&gt;" parent="WatchFaceSettingsDelegate" startPc="268438070">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" endPc="268438216" name="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;toggleSeconds&gt;" parent="WatchFaceSettingsDelegate" startPc="268438161">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268438231" name="initialize" parent="WatchFaceSettingsDelegate" startPc="268438217">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268438339" name="onSelect" parent="WatchFaceSettingsDelegate" startPc="268438232">
            <param id="item"/>
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="private" name="&lt;globals/WatchFaceSettingsDelegate/&lt;&gt;customizeDataFields&gt;" parent="WatchFaceSettingsDelegate">
            <documentation/>
        </functionEntry>
        <functionEntry accessMode="public" endPc="268438493" name="WatchFace" parent="Layouts" startPc="268438340">
            <param id="dc"/>
            <documentation/>
        </functionEntry>
    </functions>
</debugInfo>
