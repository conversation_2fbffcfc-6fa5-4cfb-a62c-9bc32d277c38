[ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 1; @symbol_importdef<0> = [Toybox,1,7,13]; @symbol_importdef<1> = [Lang,1,14,18]; ]
import Toybox.Lang;
[ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 2; @symbol_usingdef<0> = [Toybox,2,6,12]; @symbol_usingdef<1> = [WatchUi,2,13,20]; ]
using Toybox.WatchUi;
[ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 3; @symbol_usingdef<0> = [Toybox,3,6,12]; @symbol_usingdef<1> = [WatchUi,3,13,20]; @symbol_usingAlias = [Ui,3,24,26]; ]
using Toybox.WatchUi as :Ui;
[ @file = "d:\<PERSON>pa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 4; @symbol_usingdef<0> = [Toybox,4,6,12]; @symbol_usingdef<1> = [Graphics,4,13,21]; ]
using Toybox.Graphics;
[ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 5; @symbol_usingdef<0> = [Toybox,5,6,12]; @symbol_usingdef<1> = [Graphics,5,13,21]; @symbol_usingAlias = [Gfx,5,25,28]; ]
using Toybox.Graphics as :Gfx;
[ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 7; @symbol_moduledef = [Rez,7,7,10]; ]
module Rez {
    [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 7; ]
    <init> {
    }
    [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 8; @symbol_moduledef = [Drawables,8,11,20]; ]
    module Drawables {
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 8; ]
        <init> {
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 9; @position = 27; initialized = true; @symbol_vardef = [LauncherIcon,9,27,39]; @symbol_type<0> = [ResourceId,9,43,53]; ]
        var LauncherIcon as ResourceId;
    }
    [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 12; @symbol_moduledef = [Layouts,12,11,18]; ]
    module Layouts {
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 12; ]
        <init> {
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 13; @symbol_functiondef = [WatchFace,13,17,26]; @symbol_param<0> = [dc,13,27,29]; @symbol_param<0>_type<0> = [Graphics,13,33,41]; @symbol_param<0>_type<1> = [Dc,13,42,44]; @symbol_return<0> = [Array,13,49,54]; @symbol_return<1> = [WatchUi,13,55,62]; @symbol_return<2> = [Drawable,13,63,71]; ]
        function WatchFace(dc as Graphics.Dc) as Array<WatchUi.Drawable> {
d_Melpa_Madre_bin_gen_006_B4587_00_source_Rez_mcgen_13_73_19_8_start:
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 14 12 ]
            [ "d_Melpa_Madre_bin_gen_006_B4587_00_source_Rez_mcgen_13_73_19_8_start" "d_Melpa_Madre_bin_gen_006_B4587_00_source_Rez_mcgen_13_73_19_8_stop" ]
            %rez_cmp_local_custom_drawable_11939594661139614796.1 = local;
            symbol [ rez_cmp_local_custom_drawable_11939594661139614796 %rez_cmp_local_custom_drawable_11939594661139614796.1 14 16 66 ];
            symbol [ Background %tmp.4 14 73 83 ];
            %tmp.4 = getv ? :Background;
            %tmp.1 = newc %tmp.4 ();
            lputv %rez_cmp_local_custom_drawable_11939594661139614796.1 %tmp.1;
            symbol [ rez_cmp_local_custom_drawable_11939594661139614796 %rez_cmp_local_custom_drawable_11939594661139614796.1 14 16 66 ];
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 16 12 ]
            [ "d_Melpa_Madre_bin_gen_006_B4587_00_source_Rez_mcgen_13_73_19_8_start" "d_Melpa_Madre_bin_gen_006_B4587_00_source_Rez_mcgen_13_73_19_8_stop" ]
            %rez_cmp_local_text_TimeLabel.2 = local;
            symbol [ rez_cmp_local_text_TimeLabel %rez_cmp_local_text_TimeLabel.2 16 16 44 ];
            symbol [ WatchUi %tmp.7 16 51 58 ];
            %tmp.7 = getm $.Toybox.WatchUi;
            symbol [ Text %tmp.8 16 59 63 ];
            %tmp.8 = getv function ? %tmp.7 :Text;
            %tmp.9 = newd 6;
            %tmp.11 = const :identifier;
            symbol [ identifier %tmp.11 16 66 76 const ];
            %tmp.12 = "TimeLabel";
            %tmp.13 = dup %tmp.9;
            %tmp.14 = aputv %tmp.13 %tmp.11 %tmp.12;
            %tmp.16 = const :color;
            symbol [ color %tmp.16 16 92 97 const ];
            symbol [ Graphics %tmp.17 16 99 107 ];
            %tmp.17 = getm $.Toybox.Graphics;
            symbol [ COLOR_BLUE %tmp.18 16 108 118 ];
            %tmp.18 = getv %tmp.17 :COLOR_BLUE;
            %tmp.19 = dup %tmp.14;
            %tmp.20 = aputv %tmp.19 %tmp.16 %tmp.18;
            %tmp.22 = const :locX;
            symbol [ locX %tmp.22 16 121 125 const ];
            %tmp.23 = 416;
            %tmp.24 = 0.5;
            %tmp.25 = mul %tmp.23 %tmp.24;
            %tmp.26 = dup %tmp.20;
            %tmp.27 = aputv %tmp.26 %tmp.22 %tmp.25;
            %tmp.29 = const :locY;
            symbol [ locY %tmp.29 16 141 145 const ];
            %tmp.30 = 416;
            %tmp.31 = lgetv %dc;
            symbol [ dc %tmp.31 16 155 157 ];
            symbol [ getFontHeight %tmp.32 16 158 171 ];
            %tmp.32 = getv function %tmp.31 :getFontHeight;
            symbol [ Graphics %tmp.33 16 172 180 ];
            %tmp.33 = getm $.Toybox.Graphics;
            symbol [ FONT_LARGE %tmp.34 16 181 191 ];
            %tmp.34 = getv %tmp.33 :FONT_LARGE;
            %tmp.35 = invoke %tmp.31 %tmp.32(%tmp.34);
            %tmp.36 = sub %tmp.30 %tmp.35;
            %tmp.37 = 0.5;
            %tmp.38 = mul %tmp.36 %tmp.37;
            %tmp.39 = dup %tmp.27;
            %tmp.40 = aputv %tmp.39 %tmp.29 %tmp.38;
            %tmp.42 = const :justification;
            symbol [ justification %tmp.42 16 203 216 const ];
            symbol [ Graphics %tmp.43 16 218 226 ];
            %tmp.43 = getm $.Toybox.Graphics;
            symbol [ TEXT_JUSTIFY_CENTER %tmp.44 16 227 246 ];
            %tmp.44 = getv %tmp.43 :TEXT_JUSTIFY_CENTER;
            %tmp.45 = dup %tmp.40;
            %tmp.46 = aputv %tmp.45 %tmp.42 %tmp.44;
            %tmp.48 = const :font;
            symbol [ font %tmp.48 16 249 253 const ];
            symbol [ Graphics %tmp.49 16 255 263 ];
            %tmp.49 = getm $.Toybox.Graphics;
            symbol [ FONT_LARGE %tmp.50 16 264 274 ];
            %tmp.50 = getv %tmp.49 :FONT_LARGE;
            %tmp.51 = dup %tmp.46;
            %tmp.52 = aputv %tmp.51 %tmp.48 %tmp.50;
            %tmp.5 = newc %tmp.8 (%tmp.52);
            lputv %rez_cmp_local_text_TimeLabel.2 %tmp.5;
            symbol [ rez_cmp_local_text_TimeLabel %rez_cmp_local_text_TimeLabel.2 16 16 44 ];
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 18 12 ]
            %tmp.53 = newa 2 Array<WatchUi.Drawable>;
            symbol [ Array %tmp.53 18 105 110 ];
            symbol [ WatchUi %tmp.53 18 111 118 ];
            symbol [ Drawable %tmp.53 18 119 127 ];
            %tmp.54 = lgetv %rez_cmp_local_custom_drawable_11939594661139614796.1;
            symbol [ rez_cmp_local_custom_drawable_11939594661139614796 %tmp.54 18 20 70 ];
            %tmp.55 = dup %tmp.53;
            %tmp.56 = aputv %tmp.55 0 %tmp.54;
            %tmp.57 = lgetv %rez_cmp_local_text_TimeLabel.2;
            symbol [ rez_cmp_local_text_TimeLabel %tmp.57 18 72 100 ];
            %tmp.58 = dup %tmp.56;
            %tmp.59 = aputv %tmp.58 1 %tmp.57;
            ret %tmp.59;
d_Melpa_Madre_bin_gen_006_B4587_00_source_Rez_mcgen_13_73_19_8_stop:
        }
    }
    [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 22; @symbol_moduledef = [Strings,22,11,18]; ]
    module Strings {
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 22; ]
        <init> {
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 23; @position = 27; initialized = true; @symbol_vardef = [ColorLightGray,23,27,41]; @symbol_type<0> = [ResourceId,23,45,55]; ]
        var ColorLightGray as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 24; @position = 27; initialized = true; @symbol_vardef = [ColorBlue,24,27,36]; @symbol_type<0> = [ResourceId,24,40,50]; ]
        var ColorBlue as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 25; @position = 27; initialized = true; @symbol_vardef = [ColorRed,25,27,35]; @symbol_type<0> = [ResourceId,25,39,49]; ]
        var ColorRed as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 26; @position = 27; initialized = true; @symbol_vardef = [ForegroundColorTitle,26,27,47]; @symbol_type<0> = [ResourceId,26,51,61]; ]
        var ForegroundColorTitle as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 27; @position = 27; initialized = true; @symbol_vardef = [BackgroundColorTitle,27,27,47]; @symbol_type<0> = [ResourceId,27,51,61]; ]
        var BackgroundColorTitle as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 28; @position = 27; initialized = true; @symbol_vardef = [ColorDarkGray,28,27,40]; @symbol_type<0> = [ResourceId,28,44,54]; ]
        var ColorDarkGray as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 29; @position = 27; initialized = true; @symbol_vardef = [MilitaryFormatTitle,29,27,46]; @symbol_type<0> = [ResourceId,29,50,60]; ]
        var MilitaryFormatTitle as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 30; @position = 27; initialized = true; @symbol_vardef = [ColorBlack,30,27,37]; @symbol_type<0> = [ResourceId,30,41,51]; ]
        var ColorBlack as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 31; @position = 27; initialized = true; @symbol_vardef = [ColorWhite,31,27,37]; @symbol_type<0> = [ResourceId,31,41,51]; ]
        var ColorWhite as ResourceId;
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 32; @position = 27; initialized = true; @symbol_vardef = [AppName,32,27,34]; @symbol_type<0> = [ResourceId,32,38,48]; ]
        var AppName as ResourceId;
    }
    [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 35; @symbol_moduledef = [Styles,35,11,17]; ]
    module Styles {
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 35; ]
        <init> {
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 36; @symbol_moduledef = [prompt_font_enhanced_readability__body_with_title,36,15,64]; ]
        module prompt_font_enhanced_readability__body_with_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 36; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 37; @position = 18; @symbol_constdef = [exclude,37,18,25]; @symbol_type<0> = [Boolean,37,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 40; @symbol_moduledef = [system_size__menu_header,40,15,39]; ]
        module system_size__menu_header {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 40; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 41; @position = 18; @symbol_constdef = [width,41,18,23]; @symbol_type<0> = [Number,41,27,33]; ]
            const width as Number = 416;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 42; @position = 18; @symbol_constdef = [height,42,18,24]; @symbol_type<0> = [Number,42,28,34]; ]
            const height as Number = 141;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 45; @symbol_moduledef = [system_loc__hint_button_left_bottom,45,15,50]; ]
        module system_loc__hint_button_left_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 45; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 46; @position = 18; @symbol_constdef = [x,46,18,19]; @symbol_type<0> = [Number,46,23,29]; ]
            const x as Number = 0;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 47; @position = 18; @symbol_constdef = [y,47,18,19]; @symbol_type<0> = [Number,47,23,29]; ]
            const y as Number = 270;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 50; @symbol_moduledef = [prompt_font_enhanced_readability__body_no_title,50,15,62]; ]
        module prompt_font_enhanced_readability__body_no_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 50; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 51; @position = 18; @symbol_constdef = [exclude,51,18,25]; @symbol_type<0> = [Boolean,51,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 54; @symbol_moduledef = [prompt_loc__body_no_title,54,15,40]; ]
        module prompt_loc__body_no_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 54; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 55; @position = 18; @symbol_constdef = [x,55,18,19]; @symbol_type<0> = [Number,55,23,29]; ]
            const x as Number = 42;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 56; @position = 18; @symbol_constdef = [y,56,18,19]; @symbol_type<0> = [Number,56,23,29]; ]
            const y as Number = 42;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 59; @symbol_moduledef = [system_icon_dark__about,59,15,38]; ]
        module system_icon_dark__about {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 59; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 60; @position = 18; @symbol_constdef = [filename,60,18,26]; @symbol_type<0> = [String,60,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__about.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 61; @position = 18; @symbol_constdef = [compress,61,18,26]; @symbol_type<0> = [String,61,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 62; @position = 18; @symbol_constdef = [dithering,62,18,27]; @symbol_type<0> = [String,62,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 63; @position = 18; @symbol_constdef = [automaticPalette,63,18,34]; @symbol_type<0> = [String,63,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 66; @symbol_moduledef = [system_icon_light__question,66,15,42]; ]
        module system_icon_light__question {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 66; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 67; @position = 18; @symbol_constdef = [filename,67,18,26]; @symbol_type<0> = [String,67,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__question.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 68; @position = 18; @symbol_constdef = [dithering,68,18,27]; @symbol_type<0> = [String,68,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 71; @symbol_moduledef = [system_loc__hint_button_left_top,71,15,47]; ]
        module system_loc__hint_button_left_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 71; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 72; @position = 18; @symbol_constdef = [x,72,18,19]; @symbol_type<0> = [Number,72,23,29]; ]
            const x as Number = 0;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 73; @position = 18; @symbol_constdef = [y,73,18,19]; @symbol_type<0> = [Number,73,23,29]; ]
            const y as Number = 73;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 76; @symbol_moduledef = [prompt_font__title,76,15,33]; ]
        module prompt_font__title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 76; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 77 18 ]
                symbol [ Graphics %tmp.60 77 34 42 ];
                %tmp.60 = getm $.Toybox.Graphics;
                symbol [ TEXT_JUSTIFY_CENTER %tmp.61 77 43 62 ];
                %tmp.61 = getv %tmp.60 :TEXT_JUSTIFY_CENTER;
                symbol [ Graphics %tmp.62 77 63 71 ];
                %tmp.62 = getm $.Toybox.Graphics;
                symbol [ TEXT_JUSTIFY_VCENTER %tmp.63 77 72 92 ];
                %tmp.63 = getv %tmp.62 :TEXT_JUSTIFY_VCENTER;
                %tmp.64 = bitor %tmp.61 %tmp.63;
                putv self :justification %tmp.64;
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 78 18 ]
                symbol [ Graphics %tmp.65 78 25 33 ];
                %tmp.65 = getm $.Toybox.Graphics;
                symbol [ FONT_XTINY %tmp.66 78 34 44 ];
                %tmp.66 = getv %tmp.65 :FONT_XTINY;
                putv self :font %tmp.66;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 77; @position = 18; @symbol_constdef = [justification,77,18,31]; ]
            const justification;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 78; @position = 18; @symbol_constdef = [font,78,18,22]; ]
            const font;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 81; @symbol_moduledef = [system_icon_dark__discard,81,15,40]; ]
        module system_icon_dark__discard {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 81; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 82; @position = 18; @symbol_constdef = [filename,82,18,26]; @symbol_type<0> = [String,82,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__discard.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 83; @position = 18; @symbol_constdef = [compress,83,18,26]; @symbol_type<0> = [String,83,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 84; @position = 18; @symbol_constdef = [dithering,84,18,27]; @symbol_type<0> = [String,84,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 85; @position = 18; @symbol_constdef = [automaticPalette,85,18,34]; @symbol_type<0> = [String,85,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 88; @symbol_moduledef = [system_size__menu_item,88,15,37]; ]
        module system_size__menu_item {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 88; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 89; @position = 18; @symbol_constdef = [width,89,18,23]; @symbol_type<0> = [Number,89,27,33]; ]
            const width as Number = 416;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 90; @position = 18; @symbol_constdef = [height,90,18,24]; @symbol_type<0> = [Number,90,28,34]; ]
            const height as Number = 134;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 93; @symbol_moduledef = [system_color_dark__text,93,15,38]; ]
        module system_color_dark__text {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 93; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 95 18 ]
                symbol [ Graphics %tmp.67 95 31 39 ];
                %tmp.67 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.68 95 40 57 ];
                %tmp.68 = getv %tmp.67 :COLOR_TRANSPARENT;
                putv self :background %tmp.68;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 94; @position = 18; @symbol_constdef = [color,94,18,23]; @symbol_type<0> = [Number,94,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 95; @position = 18; @symbol_constdef = [background,95,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 98; @symbol_moduledef = [system_icon_destructive__discard,98,15,47]; ]
        module system_icon_destructive__discard {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 98; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 99; @position = 18; @symbol_constdef = [filename,99,18,26]; @symbol_type<0> = [String,99,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__discard.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 100; @position = 18; @symbol_constdef = [dithering,100,18,27]; @symbol_type<0> = [String,100,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 103; @symbol_moduledef = [system_icon_dark__hint_action_menu,103,15,49]; ]
        module system_icon_dark__hint_action_menu {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 103; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 104; @position = 18; @symbol_constdef = [filename,104,18,26]; @symbol_type<0> = [String,104,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 105; @position = 18; @symbol_constdef = [dithering,105,18,27]; @symbol_type<0> = [String,105,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 106; @position = 18; @symbol_constdef = [automaticPalette,106,18,34]; @symbol_type<0> = [Boolean,106,38,45]; ]
            const automaticPalette as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 109; @symbol_moduledef = [prompt_size__title,109,15,33]; ]
        module prompt_size__title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 109; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 110; @position = 18; @symbol_constdef = [width,110,18,23]; @symbol_type<0> = [Number,110,27,33]; ]
            const width as Number = 333;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 111; @position = 18; @symbol_constdef = [height,111,18,24]; @symbol_type<0> = [Number,111,28,34]; ]
            const height as Number = 63;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 114; @symbol_moduledef = [confirmation_font__body,114,15,38]; ]
        module confirmation_font__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 114; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 115 18 ]
                symbol [ Graphics %tmp.69 115 34 42 ];
                %tmp.69 = getm $.Toybox.Graphics;
                symbol [ TEXT_JUSTIFY_CENTER %tmp.70 115 43 62 ];
                %tmp.70 = getv %tmp.69 :TEXT_JUSTIFY_CENTER;
                symbol [ Graphics %tmp.71 115 63 71 ];
                %tmp.71 = getm $.Toybox.Graphics;
                symbol [ TEXT_JUSTIFY_VCENTER %tmp.72 115 72 92 ];
                %tmp.72 = getv %tmp.71 :TEXT_JUSTIFY_VCENTER;
                %tmp.73 = bitor %tmp.70 %tmp.72;
                putv self :justification %tmp.73;
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 116 18 ]
                symbol [ Graphics %tmp.74 116 25 33 ];
                %tmp.74 = getm $.Toybox.Graphics;
                symbol [ FONT_TINY %tmp.75 116 34 43 ];
                %tmp.75 = getv %tmp.74 :FONT_TINY;
                putv self :font %tmp.75;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 115; @position = 18; @symbol_constdef = [justification,115,18,31]; ]
            const justification;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 116; @position = 18; @symbol_constdef = [font,116,18,22]; ]
            const font;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 119; @symbol_moduledef = [system_color_dark__background,119,15,44]; ]
        module system_color_dark__background {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 119; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 120; @position = 18; @symbol_constdef = [color,120,18,23]; @symbol_type<0> = [Number,120,27,33]; ]
            const color as Number = 0x000000;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 121; @position = 18; @symbol_constdef = [background,121,18,28]; @symbol_type<0> = [Number,121,32,38]; ]
            const background as Number = 0x000000;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 124; @symbol_moduledef = [system_icon_destructive__hint_button_left_top,124,15,60]; ]
        module system_icon_destructive__hint_button_left_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 124; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 125; @position = 18; @symbol_constdef = [filename,125,18,26]; @symbol_type<0> = [String,125,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_left_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 126; @position = 18; @symbol_constdef = [dithering,126,18,27]; @symbol_type<0> = [String,126,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 129; @symbol_moduledef = [system_size__launch_icon,129,15,39]; ]
        module system_size__launch_icon {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 129; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 130; @position = 18; @symbol_constdef = [scaleX,130,18,24]; @symbol_type<0> = [Number,130,28,34]; ]
            const scaleX as Number = 60;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 131; @position = 18; @symbol_constdef = [scaleY,131,18,24]; @symbol_type<0> = [Number,131,28,34]; ]
            const scaleY as Number = 60;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 132; @position = 18; @symbol_constdef = [scaleRelativeTo,132,18,33]; @symbol_type<0> = [String,132,37,43]; ]
            const scaleRelativeTo as String = "screen";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 135; @symbol_moduledef = [confirmation_input__confirm,135,15,42]; ]
        module confirmation_input__confirm {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 135; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 136 18 ]
                symbol [ WatchUi %tmp.76 136 27 34 ];
                %tmp.76 = getm $.Toybox.WatchUi;
                symbol [ KEY_ENTER %tmp.77 136 35 44 ];
                %tmp.77 = getv %tmp.76 :KEY_ENTER;
                putv self :button %tmp.77;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 136; @position = 18; @symbol_constdef = [button,136,18,24]; ]
            const button;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 139; @symbol_moduledef = [prompt_font__body_with_title,139,15,43]; ]
        module prompt_font__body_with_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 139; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 140 18 ]
                symbol [ Graphics %tmp.78 140 34 42 ];
                %tmp.78 = getm $.Toybox.Graphics;
                symbol [ TEXT_JUSTIFY_CENTER %tmp.79 140 43 62 ];
                %tmp.79 = getv %tmp.78 :TEXT_JUSTIFY_CENTER;
                putv self :justification %tmp.79;
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 141 18 ]
                symbol [ Graphics %tmp.80 141 25 33 ];
                %tmp.80 = getm $.Toybox.Graphics;
                symbol [ FONT_TINY %tmp.81 141 34 43 ];
                %tmp.81 = getv %tmp.80 :FONT_TINY;
                putv self :font %tmp.81;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 140; @position = 18; @symbol_constdef = [justification,140,18,31]; ]
            const justification;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 141; @position = 18; @symbol_constdef = [font,141,18,22]; ]
            const font;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 144; @symbol_moduledef = [confirmation_loc__body,144,15,37]; ]
        module confirmation_loc__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 144; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 145; @position = 18; @symbol_constdef = [x,145,18,19]; @symbol_type<0> = [Number,145,23,29]; ]
            const x as Number = 83;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 146; @position = 18; @symbol_constdef = [y,146,18,19]; @symbol_type<0> = [Number,146,23,29]; ]
            const y as Number = 83;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 149; @symbol_moduledef = [system_icon_destructive__hint_button_left_bottom,149,15,63]; ]
        module system_icon_destructive__hint_button_left_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 149; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 150; @position = 18; @symbol_constdef = [filename,150,18,26]; @symbol_type<0> = [String,150,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_left_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 151; @position = 18; @symbol_constdef = [dithering,151,18,27]; @symbol_type<0> = [String,151,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 154; @symbol_moduledef = [prompt_color_dark__background,154,15,44]; ]
        module prompt_color_dark__background {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 154; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 155; @position = 18; @symbol_constdef = [color,155,18,23]; @symbol_type<0> = [Number,155,27,33]; ]
            const color as Number = 0x000000;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 156; @position = 18; @symbol_constdef = [background,156,18,28]; @symbol_type<0> = [Number,156,32,38]; ]
            const background as Number = 0x000000;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 159; @symbol_moduledef = [system_icon_destructive__hint_action_menu,159,15,56]; ]
        module system_icon_destructive__hint_action_menu {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 159; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 160; @position = 18; @symbol_constdef = [filename,160,18,26]; @symbol_type<0> = [String,160,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 161; @position = 18; @symbol_constdef = [dithering,161,18,27]; @symbol_type<0> = [String,161,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 162; @position = 18; @symbol_constdef = [automaticPalette,162,18,34]; @symbol_type<0> = [Boolean,162,38,45]; ]
            const automaticPalette as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 165; @symbol_moduledef = [system_loc__hint_action_menu,165,15,43]; ]
        module system_loc__hint_action_menu {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 165; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 166; @position = 18; @symbol_constdef = [x,166,18,19]; @symbol_type<0> = [Number,166,23,29]; ]
            const x as Number = 354;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 167; @position = 18; @symbol_constdef = [y,167,18,19]; @symbol_type<0> = [Number,167,23,29]; ]
            const y as Number = 73;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 170; @symbol_moduledef = [confirmation_icon__hint_confirm,170,15,46]; ]
        module confirmation_icon__hint_confirm {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 170; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 171; @position = 18; @symbol_constdef = [filename,171,18,26]; @symbol_type<0> = [String,171,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_completion.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 172; @position = 18; @symbol_constdef = [dithering,172,18,27]; @symbol_type<0> = [String,172,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 175; @symbol_moduledef = [system_loc__hint_button_left_middle,175,15,50]; ]
        module system_loc__hint_button_left_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 175; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 176; @position = 18; @symbol_constdef = [x,176,18,19]; @symbol_type<0> = [Number,176,23,29]; ]
            const x as Number = 0;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 177; @position = 18; @symbol_constdef = [y,177,18,19]; @symbol_type<0> = [Number,177,23,29]; ]
            const y as Number = 169;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 180; @symbol_moduledef = [prompt_size__body_no_title,180,15,41]; ]
        module prompt_size__body_no_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 180; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 181; @position = 18; @symbol_constdef = [width,181,18,23]; @symbol_type<0> = [Number,181,27,33]; ]
            const width as Number = 333;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 182; @position = 18; @symbol_constdef = [height,182,18,24]; @symbol_type<0> = [Number,182,28,34]; ]
            const height as Number = 333;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 185; @symbol_moduledef = [system_icon_light__hint_button_left_top,185,15,54]; ]
        module system_icon_light__hint_button_left_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 185; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 186; @position = 18; @symbol_constdef = [filename,186,18,26]; @symbol_type<0> = [String,186,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 187; @position = 18; @symbol_constdef = [dithering,187,18,27]; @symbol_type<0> = [String,187,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 190; @symbol_moduledef = [prompt_color_light__background,190,15,45]; ]
        module prompt_color_light__background {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 190; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 191; @position = 18; @symbol_constdef = [color,191,18,23]; @symbol_type<0> = [Number,191,27,33]; ]
            const color as Number = 0x000000;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 192; @position = 18; @symbol_constdef = [background,192,18,28]; @symbol_type<0> = [Number,192,32,38]; ]
            const background as Number = 0x000000;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 195; @symbol_moduledef = [confirmation_input__delete,195,15,41]; ]
        module confirmation_input__delete {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 195; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 196 18 ]
                symbol [ WatchUi %tmp.82 196 27 34 ];
                %tmp.82 = getm $.Toybox.WatchUi;
                symbol [ KEY_ENTER %tmp.83 196 35 44 ];
                %tmp.83 = getv %tmp.82 :KEY_ENTER;
                putv self :button %tmp.83;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 196; @position = 18; @symbol_constdef = [button,196,18,24]; ]
            const button;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 199; @symbol_moduledef = [system_icon_dark__hint_button_left_top,199,15,53]; ]
        module system_icon_dark__hint_button_left_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 199; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 200; @position = 18; @symbol_constdef = [filename,200,18,26]; @symbol_type<0> = [String,200,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 201; @position = 18; @symbol_constdef = [dithering,201,18,27]; @symbol_type<0> = [String,201,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 204; @symbol_moduledef = [system_input__action_menu,204,15,40]; ]
        module system_input__action_menu {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 204; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 205 18 ]
                symbol [ WatchUi %tmp.84 205 27 34 ];
                %tmp.84 = getm $.Toybox.WatchUi;
                symbol [ KEY_ENTER %tmp.85 205 35 44 ];
                %tmp.85 = getv %tmp.84 :KEY_ENTER;
                putv self :button %tmp.85;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 205; @position = 18; @symbol_constdef = [button,205,18,24]; ]
            const button;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 208; @symbol_moduledef = [system_icon_light__save,208,15,38]; ]
        module system_icon_light__save {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 208; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 209; @position = 18; @symbol_constdef = [filename,209,18,26]; @symbol_type<0> = [String,209,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__save.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 210; @position = 18; @symbol_constdef = [dithering,210,18,27]; @symbol_type<0> = [String,210,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 213; @symbol_moduledef = [prompt_color_light__body,213,15,39]; ]
        module prompt_color_light__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 213; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 215 18 ]
                symbol [ Graphics %tmp.86 215 31 39 ];
                %tmp.86 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.87 215 40 57 ];
                %tmp.87 = getv %tmp.86 :COLOR_TRANSPARENT;
                putv self :background %tmp.87;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 214; @position = 18; @symbol_constdef = [color,214,18,23]; @symbol_type<0> = [Number,214,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 215; @position = 18; @symbol_constdef = [background,215,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 218; @symbol_moduledef = [system_loc__subwindow,218,15,36]; ]
        module system_loc__subwindow {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 218; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 219; @position = 18; @symbol_constdef = [exclude,219,18,25]; @symbol_type<0> = [Boolean,219,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 222; @symbol_moduledef = [system_icon_light__warning,222,15,41]; ]
        module system_icon_light__warning {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 222; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 223; @position = 18; @symbol_constdef = [filename,223,18,26]; @symbol_type<0> = [String,223,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__warning.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 224; @position = 18; @symbol_constdef = [dithering,224,18,27]; @symbol_type<0> = [String,224,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 227; @symbol_moduledef = [confirmation_color_light__body,227,15,45]; ]
        module confirmation_color_light__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 227; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 229 18 ]
                symbol [ Graphics %tmp.88 229 31 39 ];
                %tmp.88 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.89 229 40 57 ];
                %tmp.89 = getv %tmp.88 :COLOR_TRANSPARENT;
                putv self :background %tmp.89;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 228; @position = 18; @symbol_constdef = [color,228,18,23]; @symbol_type<0> = [Number,228,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 229; @position = 18; @symbol_constdef = [background,229,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 232; @symbol_moduledef = [confirmation_icon_light__hint_keep,232,15,49]; ]
        module confirmation_icon_light__hint_keep {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 232; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 233; @position = 18; @symbol_constdef = [filename,233,18,26]; @symbol_type<0> = [String,233,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_keep.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 234; @position = 18; @symbol_constdef = [dithering,234,18,27]; @symbol_type<0> = [String,234,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 237; @symbol_moduledef = [activity_color_light__background,237,15,47]; ]
        module activity_color_light__background {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 237; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 238; @position = 18; @symbol_constdef = [color,238,18,23]; @symbol_type<0> = [Number,238,27,33]; ]
            const color as Number = 0x000000;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 239; @position = 18; @symbol_constdef = [background,239,18,28]; @symbol_type<0> = [Number,239,32,38]; ]
            const background as Number = 0x000000;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 242; @symbol_moduledef = [system_icon_positive__hint_button_right_top,242,15,58]; ]
        module system_icon_positive__hint_button_right_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 242; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 243; @position = 18; @symbol_constdef = [filename,243,18,26]; @symbol_type<0> = [String,243,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 244; @position = 18; @symbol_constdef = [dithering,244,18,27]; @symbol_type<0> = [String,244,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 247; @symbol_moduledef = [system_size__screen,247,15,34]; ]
        module system_size__screen {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 247; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 248; @position = 18; @symbol_constdef = [width,248,18,23]; @symbol_type<0> = [Number,248,27,33]; ]
            const width as Number = 416;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 249; @position = 18; @symbol_constdef = [height,249,18,24]; @symbol_type<0> = [Number,249,28,34]; ]
            const height as Number = 416;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 252; @symbol_moduledef = [system_icon_destructive__hint_button_right_bottom,252,15,64]; ]
        module system_icon_destructive__hint_button_right_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 252; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 253; @position = 18; @symbol_constdef = [filename,253,18,26]; @symbol_type<0> = [String,253,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_right_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 254; @position = 18; @symbol_constdef = [dithering,254,18,27]; @symbol_type<0> = [String,254,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 257; @symbol_moduledef = [system_color_light__background,257,15,45]; ]
        module system_color_light__background {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 257; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 258; @position = 18; @symbol_constdef = [color,258,18,23]; @symbol_type<0> = [Number,258,27,33]; ]
            const color as Number = 0x000000;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 259; @position = 18; @symbol_constdef = [background,259,18,28]; @symbol_type<0> = [Number,259,32,38]; ]
            const background as Number = 0x000000;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 262; @symbol_moduledef = [system_icon_destructive__hint_button_right_top,262,15,61]; ]
        module system_icon_destructive__hint_button_right_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 262; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 263; @position = 18; @symbol_constdef = [filename,263,18,26]; @symbol_type<0> = [String,263,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 264; @position = 18; @symbol_constdef = [dithering,264,18,27]; @symbol_type<0> = [String,264,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 267; @symbol_moduledef = [confirmation_loc__hint_confirm,267,15,45]; ]
        module confirmation_loc__hint_confirm {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 267; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 268; @position = 18; @symbol_constdef = [x,268,18,19]; @symbol_type<0> = [Number,268,23,29]; ]
            const x as Number = 306;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 269; @position = 18; @symbol_constdef = [y,269,18,19]; @symbol_type<0> = [Number,269,23,29]; ]
            const y as Number = 0;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 272; @symbol_moduledef = [activity_color_dark__text,272,15,40]; ]
        module activity_color_dark__text {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 272; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 274 18 ]
                symbol [ Graphics %tmp.90 274 31 39 ];
                %tmp.90 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.91 274 40 57 ];
                %tmp.91 = getv %tmp.90 :COLOR_TRANSPARENT;
                putv self :background %tmp.91;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 273; @position = 18; @symbol_constdef = [color,273,18,23]; @symbol_type<0> = [Number,273,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 274; @position = 18; @symbol_constdef = [background,274,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 277; @symbol_moduledef = [confirmation_input__keep,277,15,39]; ]
        module confirmation_input__keep {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 277; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 278 18 ]
                symbol [ WatchUi %tmp.92 278 27 34 ];
                %tmp.92 = getm $.Toybox.WatchUi;
                symbol [ KEY_DOWN %tmp.93 278 35 43 ];
                %tmp.93 = getv %tmp.92 :KEY_DOWN;
                putv self :button %tmp.93;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 278; @position = 18; @symbol_constdef = [button,278,18,24]; ]
            const button;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 281; @symbol_moduledef = [prompt_loc__body_with_title,281,15,42]; ]
        module prompt_loc__body_with_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 281; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 282; @position = 18; @symbol_constdef = [x,282,18,19]; @symbol_type<0> = [Number,282,23,29]; ]
            const x as Number = 42;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 283; @position = 18; @symbol_constdef = [y,283,18,19]; @symbol_type<0> = [Number,283,23,29]; ]
            const y as Number = 125;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 286; @symbol_moduledef = [system_icon_dark__search,286,15,39]; ]
        module system_icon_dark__search {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 286; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 287; @position = 18; @symbol_constdef = [filename,287,18,26]; @symbol_type<0> = [String,287,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__search.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 288; @position = 18; @symbol_constdef = [compress,288,18,26]; @symbol_type<0> = [String,288,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 289; @position = 18; @symbol_constdef = [dithering,289,18,27]; @symbol_type<0> = [String,289,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 290; @position = 18; @symbol_constdef = [automaticPalette,290,18,34]; @symbol_type<0> = [String,290,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 293; @symbol_moduledef = [activity_color_light__text,293,15,41]; ]
        module activity_color_light__text {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 293; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 295 18 ]
                symbol [ Graphics %tmp.94 295 31 39 ];
                %tmp.94 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.95 295 40 57 ];
                %tmp.95 = getv %tmp.94 :COLOR_TRANSPARENT;
                putv self :background %tmp.95;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 294; @position = 18; @symbol_constdef = [color,294,18,23]; @symbol_type<0> = [Number,294,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 295; @position = 18; @symbol_constdef = [background,295,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 298; @symbol_moduledef = [confirmation_icon__hint_keep,298,15,43]; ]
        module confirmation_icon__hint_keep {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 298; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 299; @position = 18; @symbol_constdef = [filename,299,18,26]; @symbol_type<0> = [String,299,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_keep.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 300; @position = 18; @symbol_constdef = [dithering,300,18,27]; @symbol_type<0> = [String,300,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 303; @symbol_moduledef = [system_icon_dark__save,303,15,37]; ]
        module system_icon_dark__save {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 303; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 304; @position = 18; @symbol_constdef = [filename,304,18,26]; @symbol_type<0> = [String,304,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__save.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 305; @position = 18; @symbol_constdef = [compress,305,18,26]; @symbol_type<0> = [String,305,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 306; @position = 18; @symbol_constdef = [dithering,306,18,27]; @symbol_type<0> = [String,306,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 307; @position = 18; @symbol_constdef = [automaticPalette,307,18,34]; @symbol_type<0> = [String,307,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 310; @symbol_moduledef = [system_icon_destructive__hint_button_left_middle,310,15,63]; ]
        module system_icon_destructive__hint_button_left_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 310; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 311; @position = 18; @symbol_constdef = [filename,311,18,26]; @symbol_type<0> = [String,311,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__hint_button_left_middle.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 312; @position = 18; @symbol_constdef = [dithering,312,18,27]; @symbol_type<0> = [String,312,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 315; @symbol_moduledef = [system_icon_light__search,315,15,40]; ]
        module system_icon_light__search {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 315; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 316; @position = 18; @symbol_constdef = [filename,316,18,26]; @symbol_type<0> = [String,316,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__search.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 317; @position = 18; @symbol_constdef = [dithering,317,18,27]; @symbol_type<0> = [String,317,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 320; @symbol_moduledef = [system_icon_dark__hint_button_left_bottom,320,15,56]; ]
        module system_icon_dark__hint_button_left_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 320; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 321; @position = 18; @symbol_constdef = [filename,321,18,26]; @symbol_type<0> = [String,321,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 322; @position = 18; @symbol_constdef = [dithering,322,18,27]; @symbol_type<0> = [String,322,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 325; @symbol_moduledef = [system_icon_positive__hint_button_left_top,325,15,57]; ]
        module system_icon_positive__hint_button_left_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 325; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 326; @position = 18; @symbol_constdef = [filename,326,18,26]; @symbol_type<0> = [String,326,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_left_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 327; @position = 18; @symbol_constdef = [dithering,327,18,27]; @symbol_type<0> = [String,327,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 330; @symbol_moduledef = [system_loc__hint_button_right_bottom,330,15,51]; ]
        module system_loc__hint_button_right_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 330; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 331; @position = 18; @symbol_constdef = [x,331,18,19]; @symbol_type<0> = [Number,331,23,29]; ]
            const x as Number = 354;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 332; @position = 18; @symbol_constdef = [y,332,18,19]; @symbol_type<0> = [Number,332,23,29]; ]
            const y as Number = 270;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 335; @symbol_moduledef = [confirmation_icon_dark__hint_reject,335,15,50]; ]
        module confirmation_icon_dark__hint_reject {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 335; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 336; @position = 18; @symbol_constdef = [filename,336,18,26]; @symbol_type<0> = [String,336,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_reject.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 337; @position = 18; @symbol_constdef = [dithering,337,18,27]; @symbol_type<0> = [String,337,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 340; @symbol_moduledef = [confirmation_color_dark__body,340,15,44]; ]
        module confirmation_color_dark__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 340; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 342 18 ]
                symbol [ Graphics %tmp.96 342 31 39 ];
                %tmp.96 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.97 342 40 57 ];
                %tmp.97 = getv %tmp.96 :COLOR_TRANSPARENT;
                putv self :background %tmp.97;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 341; @position = 18; @symbol_constdef = [color,341,18,23]; @symbol_type<0> = [Number,341,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 342; @position = 18; @symbol_constdef = [background,342,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 345; @symbol_moduledef = [confirmation_icon_light__hint_confirm,345,15,52]; ]
        module confirmation_icon_light__hint_confirm {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 345; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 346; @position = 18; @symbol_constdef = [filename,346,18,26]; @symbol_type<0> = [String,346,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_completion.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 347; @position = 18; @symbol_constdef = [dithering,347,18,27]; @symbol_type<0> = [String,347,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 350; @symbol_moduledef = [prompt_color_dark__title,350,15,39]; ]
        module prompt_color_dark__title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 350; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 352 18 ]
                symbol [ Graphics %tmp.98 352 31 39 ];
                %tmp.98 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.99 352 40 57 ];
                %tmp.99 = getv %tmp.98 :COLOR_TRANSPARENT;
                putv self :background %tmp.99;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 351; @position = 18; @symbol_constdef = [color,351,18,23]; @symbol_type<0> = [Number,351,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 352; @position = 18; @symbol_constdef = [background,352,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 355; @symbol_moduledef = [system_icon_destructive__cancel,355,15,46]; ]
        module system_icon_destructive__cancel {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 355; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 356; @position = 18; @symbol_constdef = [filename,356,18,26]; @symbol_type<0> = [String,356,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__cancel.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 357; @position = 18; @symbol_constdef = [dithering,357,18,27]; @symbol_type<0> = [String,357,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 360; @symbol_moduledef = [system_icon_light__hint_button_right_bottom,360,15,58]; ]
        module system_icon_light__hint_button_right_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 360; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 361; @position = 18; @symbol_constdef = [filename,361,18,26]; @symbol_type<0> = [String,361,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 362; @position = 18; @symbol_constdef = [dithering,362,18,27]; @symbol_type<0> = [String,362,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 365; @symbol_moduledef = [prompt_loc__title,365,15,32]; ]
        module prompt_loc__title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 365; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 366; @position = 18; @symbol_constdef = [x,366,18,19]; @symbol_type<0> = [Number,366,23,29]; ]
            const x as Number = 42;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 367; @position = 18; @symbol_constdef = [y,367,18,19]; @symbol_type<0> = [Number,367,23,29]; ]
            const y as Number = 31;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 370; @symbol_moduledef = [confirmation_font_enhanced_readability__body,370,15,59]; ]
        module confirmation_font_enhanced_readability__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 370; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 371; @position = 18; @symbol_constdef = [exclude,371,18,25]; @symbol_type<0> = [Boolean,371,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 374; @symbol_moduledef = [prompt_color_light__title,374,15,40]; ]
        module prompt_color_light__title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 374; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 376 18 ]
                symbol [ Graphics %tmp.100 376 31 39 ];
                %tmp.100 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.101 376 40 57 ];
                %tmp.101 = getv %tmp.100 :COLOR_TRANSPARENT;
                putv self :background %tmp.101;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 375; @position = 18; @symbol_constdef = [color,375,18,23]; @symbol_type<0> = [Number,375,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 376; @position = 18; @symbol_constdef = [background,376,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 379; @symbol_moduledef = [system_icon_dark__hint_button_right_top,379,15,54]; ]
        module system_icon_dark__hint_button_right_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 379; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 380; @position = 18; @symbol_constdef = [filename,380,18,26]; @symbol_type<0> = [String,380,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 381; @position = 18; @symbol_constdef = [dithering,381,18,27]; @symbol_type<0> = [String,381,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 384; @symbol_moduledef = [system_icon_dark__hint_button_left_middle,384,15,56]; ]
        module system_icon_dark__hint_button_left_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 384; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 385; @position = 18; @symbol_constdef = [filename,385,18,26]; @symbol_type<0> = [String,385,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_middle.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 386; @position = 18; @symbol_constdef = [dithering,386,18,27]; @symbol_type<0> = [String,386,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 389; @symbol_moduledef = [confirmation_icon_dark__hint_keep,389,15,48]; ]
        module confirmation_icon_dark__hint_keep {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 389; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 390; @position = 18; @symbol_constdef = [filename,390,18,26]; @symbol_type<0> = [String,390,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_keep.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 391; @position = 18; @symbol_constdef = [dithering,391,18,27]; @symbol_type<0> = [String,391,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 394; @symbol_moduledef = [system_icon_light__cancel,394,15,40]; ]
        module system_icon_light__cancel {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 394; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 395; @position = 18; @symbol_constdef = [filename,395,18,26]; @symbol_type<0> = [String,395,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__cancel.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 396; @position = 18; @symbol_constdef = [dithering,396,18,27]; @symbol_type<0> = [String,396,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 399; @symbol_moduledef = [confirmation_icon_light__hint_reject,399,15,51]; ]
        module confirmation_icon_light__hint_reject {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 399; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 400; @position = 18; @symbol_constdef = [filename,400,18,26]; @symbol_type<0> = [String,400,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_reject.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 401; @position = 18; @symbol_constdef = [dithering,401,18,27]; @symbol_type<0> = [String,401,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 404; @symbol_moduledef = [system_icon_light__about,404,15,39]; ]
        module system_icon_light__about {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 404; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 405; @position = 18; @symbol_constdef = [filename,405,18,26]; @symbol_type<0> = [String,405,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__about.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 406; @position = 18; @symbol_constdef = [dithering,406,18,27]; @symbol_type<0> = [String,406,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 409; @symbol_moduledef = [system_size__toast_icon,409,15,38]; ]
        module system_size__toast_icon {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 409; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 410; @position = 18; @symbol_constdef = [scaleX,410,18,24]; @symbol_type<0> = [Number,410,28,34]; ]
            const scaleX as Number = 40;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 411; @position = 18; @symbol_constdef = [scaleY,411,18,24]; @symbol_type<0> = [Number,411,28,34]; ]
            const scaleY as Number = 40;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 412; @position = 18; @symbol_constdef = [scaleRelativeTo,412,18,33]; @symbol_type<0> = [String,412,37,43]; ]
            const scaleRelativeTo as String = "screen";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 415; @symbol_moduledef = [system_icon_dark__hint_button_right_middle,415,15,57]; ]
        module system_icon_dark__hint_button_right_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 415; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 416; @position = 18; @symbol_constdef = [exclude,416,18,25]; @symbol_type<0> = [Boolean,416,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 419; @symbol_moduledef = [confirmation_icon__hint_reject,419,15,45]; ]
        module confirmation_icon__hint_reject {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 419; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 420; @position = 18; @symbol_constdef = [filename,420,18,26]; @symbol_type<0> = [String,420,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_reject.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 421; @position = 18; @symbol_constdef = [dithering,421,18,27]; @symbol_type<0> = [String,421,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 424; @symbol_moduledef = [system_icon_light__hint_action_menu,424,15,50]; ]
        module system_icon_light__hint_action_menu {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 424; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 425; @position = 18; @symbol_constdef = [filename,425,18,26]; @symbol_type<0> = [String,425,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 426; @position = 18; @symbol_constdef = [dithering,426,18,27]; @symbol_type<0> = [String,426,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 427; @position = 18; @symbol_constdef = [automaticPalette,427,18,34]; @symbol_type<0> = [Boolean,427,38,45]; ]
            const automaticPalette as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 430; @symbol_moduledef = [system_icon_light__hint_button_right_middle,430,15,58]; ]
        module system_icon_light__hint_button_right_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 430; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 431; @position = 18; @symbol_constdef = [exclude,431,18,25]; @symbol_type<0> = [Boolean,431,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 434; @symbol_moduledef = [system_icon_destructive__hint_button_right_middle,434,15,64]; ]
        module system_icon_destructive__hint_button_right_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 434; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 435; @position = 18; @symbol_constdef = [exclude,435,18,25]; @symbol_type<0> = [Boolean,435,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 438; @symbol_moduledef = [confirmation_loc__hint_reject,438,15,44]; ]
        module confirmation_loc__hint_reject {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 438; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 439; @position = 18; @symbol_constdef = [x,439,18,19]; @symbol_type<0> = [Number,439,23,29]; ]
            const x as Number = 0;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 440; @position = 18; @symbol_constdef = [y,440,18,19]; @symbol_type<0> = [Number,440,23,29]; ]
            const y as Number = 256;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 441; @position = 18; @symbol_constdef = [horizontalJustification,441,18,41]; @symbol_type<0> = [String,441,45,51]; ]
            const horizontalJustification as String = "left";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 444; @symbol_moduledef = [confirmation_icon_dark__hint_delete,444,15,50]; ]
        module confirmation_icon_dark__hint_delete {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 444; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 445; @position = 18; @symbol_constdef = [filename,445,18,26]; @symbol_type<0> = [String,445,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_delete.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 446; @position = 18; @symbol_constdef = [dithering,446,18,27]; @symbol_type<0> = [String,446,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 449; @symbol_moduledef = [activity_color_dark__background,449,15,46]; ]
        module activity_color_dark__background {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 449; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 450; @position = 18; @symbol_constdef = [color,450,18,23]; @symbol_type<0> = [Number,450,27,33]; ]
            const color as Number = 0x000000;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 451; @position = 18; @symbol_constdef = [background,451,18,28]; @symbol_type<0> = [Number,451,32,38]; ]
            const background as Number = 0x000000;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 454; @symbol_moduledef = [system_icon_light__hint_button_left_bottom,454,15,57]; ]
        module system_icon_light__hint_button_left_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 454; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 455; @position = 18; @symbol_constdef = [filename,455,18,26]; @symbol_type<0> = [String,455,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 456; @position = 18; @symbol_constdef = [dithering,456,18,27]; @symbol_type<0> = [String,456,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 459; @symbol_moduledef = [system_icon_positive__hint_button_left_middle,459,15,60]; ]
        module system_icon_positive__hint_button_left_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 459; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 460; @position = 18; @symbol_constdef = [filename,460,18,26]; @symbol_type<0> = [String,460,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_left_middle.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 461; @position = 18; @symbol_constdef = [dithering,461,18,27]; @symbol_type<0> = [String,461,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 464; @symbol_moduledef = [system_size__menu_icon,464,15,37]; ]
        module system_size__menu_icon {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 464; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 465; @position = 18; @symbol_constdef = [scaleX,465,18,24]; @symbol_type<0> = [Number,465,28,34]; ]
            const scaleX as Number = 60;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 466; @position = 18; @symbol_constdef = [scaleY,466,18,24]; @symbol_type<0> = [Number,466,28,34]; ]
            const scaleY as Number = 60;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 467; @position = 18; @symbol_constdef = [scaleRelativeTo,467,18,33]; @symbol_type<0> = [String,467,37,43]; ]
            const scaleRelativeTo as String = "screen";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 470; @symbol_moduledef = [system_icon_destructive__warning,470,15,47]; ]
        module system_icon_destructive__warning {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 470; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 471; @position = 18; @symbol_constdef = [filename,471,18,26]; @symbol_type<0> = [String,471,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_destructive__warning.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 472; @position = 18; @symbol_constdef = [dithering,472,18,27]; @symbol_type<0> = [String,472,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 475; @symbol_moduledef = [system_loc__hint_button_right_middle,475,15,51]; ]
        module system_loc__hint_button_right_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 475; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 476; @position = 18; @symbol_constdef = [exclude,476,18,25]; @symbol_type<0> = [Boolean,476,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 479; @symbol_moduledef = [system_icon_dark__cancel,479,15,39]; ]
        module system_icon_dark__cancel {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 479; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 480; @position = 18; @symbol_constdef = [filename,480,18,26]; @symbol_type<0> = [String,480,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__cancel.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 481; @position = 18; @symbol_constdef = [compress,481,18,26]; @symbol_type<0> = [String,481,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 482; @position = 18; @symbol_constdef = [dithering,482,18,27]; @symbol_type<0> = [String,482,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 483; @position = 18; @symbol_constdef = [automaticPalette,483,18,34]; @symbol_type<0> = [String,483,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 486; @symbol_moduledef = [prompt_size__body_with_title,486,15,43]; ]
        module prompt_size__body_with_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 486; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 487; @position = 18; @symbol_constdef = [width,487,18,23]; @symbol_type<0> = [Number,487,27,33]; ]
            const width as Number = 333;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 488; @position = 18; @symbol_constdef = [height,488,18,24]; @symbol_type<0> = [Number,488,28,34]; ]
            const height as Number = 250;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 491; @symbol_moduledef = [system_loc__hint_button_right_top,491,15,48]; ]
        module system_loc__hint_button_right_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 491; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 492; @position = 18; @symbol_constdef = [x,492,18,19]; @symbol_type<0> = [Number,492,23,29]; ]
            const x as Number = 354;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 493; @position = 18; @symbol_constdef = [y,493,18,19]; @symbol_type<0> = [Number,493,23,29]; ]
            const y as Number = 73;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 496; @symbol_moduledef = [system_color_light__text,496,15,39]; ]
        module system_color_light__text {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 496; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 498 18 ]
                symbol [ Graphics %tmp.102 498 31 39 ];
                %tmp.102 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.103 498 40 57 ];
                %tmp.103 = getv %tmp.102 :COLOR_TRANSPARENT;
                putv self :background %tmp.103;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 497; @position = 18; @symbol_constdef = [color,497,18,23]; @symbol_type<0> = [Number,497,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 498; @position = 18; @symbol_constdef = [background,498,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 501; @symbol_moduledef = [system_icon_light__check,501,15,39]; ]
        module system_icon_light__check {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 501; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 502; @position = 18; @symbol_constdef = [filename,502,18,26]; @symbol_type<0> = [String,502,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__check.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 503; @position = 18; @symbol_constdef = [dithering,503,18,27]; @symbol_type<0> = [String,503,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 506; @symbol_moduledef = [confirmation_input__reject,506,15,41]; ]
        module confirmation_input__reject {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 506; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 507 18 ]
                symbol [ WatchUi %tmp.104 507 27 34 ];
                %tmp.104 = getm $.Toybox.WatchUi;
                symbol [ KEY_DOWN %tmp.105 507 35 43 ];
                %tmp.105 = getv %tmp.104 :KEY_DOWN;
                putv self :button %tmp.105;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 507; @position = 18; @symbol_constdef = [button,507,18,24]; ]
            const button;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 510; @symbol_moduledef = [system_icon_light__revert,510,15,40]; ]
        module system_icon_light__revert {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 510; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 511; @position = 18; @symbol_constdef = [filename,511,18,26]; @symbol_type<0> = [String,511,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__revert.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 512; @position = 18; @symbol_constdef = [dithering,512,18,27]; @symbol_type<0> = [String,512,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 515; @symbol_moduledef = [system_size__subwindow,515,15,37]; ]
        module system_size__subwindow {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 515; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 516; @position = 18; @symbol_constdef = [exclude,516,18,25]; @symbol_type<0> = [Boolean,516,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 519; @symbol_moduledef = [system_icon_positive__hint_button_left_bottom,519,15,60]; ]
        module system_icon_positive__hint_button_left_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 519; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 520; @position = 18; @symbol_constdef = [filename,520,18,26]; @symbol_type<0> = [String,520,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_left_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 521; @position = 18; @symbol_constdef = [dithering,521,18,27]; @symbol_type<0> = [String,521,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 524; @symbol_moduledef = [system_icon_dark__check,524,15,38]; ]
        module system_icon_dark__check {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 524; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 525; @position = 18; @symbol_constdef = [filename,525,18,26]; @symbol_type<0> = [String,525,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__check.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 526; @position = 18; @symbol_constdef = [compress,526,18,26]; @symbol_type<0> = [String,526,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 527; @position = 18; @symbol_constdef = [dithering,527,18,27]; @symbol_type<0> = [String,527,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 528; @position = 18; @symbol_constdef = [automaticPalette,528,18,34]; @symbol_type<0> = [String,528,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 531; @symbol_moduledef = [confirmation_loc__hint_keep,531,15,42]; ]
        module confirmation_loc__hint_keep {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 531; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 532; @position = 18; @symbol_constdef = [x,532,18,19]; @symbol_type<0> = [Number,532,23,29]; ]
            const x as Number = 0;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 533; @position = 18; @symbol_constdef = [y,533,18,19]; @symbol_type<0> = [Number,533,23,29]; ]
            const y as Number = 256;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 534; @position = 18; @symbol_constdef = [horizontalJustification,534,18,41]; @symbol_type<0> = [String,534,45,51]; ]
            const horizontalJustification as String = "left";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 537; @symbol_moduledef = [confirmation_loc__hint_delete,537,15,44]; ]
        module confirmation_loc__hint_delete {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 537; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 538; @position = 18; @symbol_constdef = [x,538,18,19]; @symbol_type<0> = [Number,538,23,29]; ]
            const x as Number = 306;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 539; @position = 18; @symbol_constdef = [y,539,18,19]; @symbol_type<0> = [Number,539,23,29]; ]
            const y as Number = 0;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 542; @symbol_moduledef = [system_icon_positive__hint_action_menu,542,15,53]; ]
        module system_icon_positive__hint_action_menu {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 542; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 543; @position = 18; @symbol_constdef = [filename,543,18,26]; @symbol_type<0> = [String,543,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 544; @position = 18; @symbol_constdef = [dithering,544,18,27]; @symbol_type<0> = [String,544,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 545; @position = 18; @symbol_constdef = [automaticPalette,545,18,34]; @symbol_type<0> = [Boolean,545,38,45]; ]
            const automaticPalette as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 548; @symbol_moduledef = [system_icon_positive__hint_button_right_middle,548,15,61]; ]
        module system_icon_positive__hint_button_right_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 548; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 549; @position = 18; @symbol_constdef = [exclude,549,18,25]; @symbol_type<0> = [Boolean,549,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 552; @symbol_moduledef = [system_icon_light__hint_button_left_middle,552,15,57]; ]
        module system_icon_light__hint_button_left_middle {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 552; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 553; @position = 18; @symbol_constdef = [filename,553,18,26]; @symbol_type<0> = [String,553,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_left_middle.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 554; @position = 18; @symbol_constdef = [dithering,554,18,27]; @symbol_type<0> = [String,554,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 557; @symbol_moduledef = [system_icon_dark__hint_button_right_bottom,557,15,57]; ]
        module system_icon_dark__hint_button_right_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 557; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 558; @position = 18; @symbol_constdef = [filename,558,18,26]; @symbol_type<0> = [String,558,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 559; @position = 18; @symbol_constdef = [dithering,559,18,27]; @symbol_type<0> = [String,559,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 562; @symbol_moduledef = [system_icon_positive__hint_button_right_bottom,562,15,61]; ]
        module system_icon_positive__hint_button_right_bottom {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 562; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 563; @position = 18; @symbol_constdef = [filename,563,18,26]; @symbol_type<0> = [String,563,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__hint_button_right_bottom.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 564; @position = 18; @symbol_constdef = [dithering,564,18,27]; @symbol_type<0> = [String,564,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 567; @symbol_moduledef = [system_icon_dark__revert,567,15,39]; ]
        module system_icon_dark__revert {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 567; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 568; @position = 18; @symbol_constdef = [filename,568,18,26]; @symbol_type<0> = [String,568,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__revert.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 569; @position = 18; @symbol_constdef = [compress,569,18,26]; @symbol_type<0> = [String,569,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 570; @position = 18; @symbol_constdef = [dithering,570,18,27]; @symbol_type<0> = [String,570,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 571; @position = 18; @symbol_constdef = [automaticPalette,571,18,34]; @symbol_type<0> = [String,571,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 574; @symbol_moduledef = [confirmation_icon_light__hint_delete,574,15,51]; ]
        module confirmation_icon_light__hint_delete {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 574; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 575; @position = 18; @symbol_constdef = [filename,575,18,26]; @symbol_type<0> = [String,575,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_delete.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 576; @position = 18; @symbol_constdef = [dithering,576,18,27]; @symbol_type<0> = [String,576,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 579; @symbol_moduledef = [system_icon_dark__question,579,15,41]; ]
        module system_icon_dark__question {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 579; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 580; @position = 18; @symbol_constdef = [filename,580,18,26]; @symbol_type<0> = [String,580,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__question.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 581; @position = 18; @symbol_constdef = [compress,581,18,26]; @symbol_type<0> = [String,581,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 582; @position = 18; @symbol_constdef = [dithering,582,18,27]; @symbol_type<0> = [String,582,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 583; @position = 18; @symbol_constdef = [automaticPalette,583,18,34]; @symbol_type<0> = [String,583,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 586; @symbol_moduledef = [system_icon_light__discard,586,15,41]; ]
        module system_icon_light__discard {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 586; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 587; @position = 18; @symbol_constdef = [filename,587,18,26]; @symbol_type<0> = [String,587,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__discard.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 588; @position = 18; @symbol_constdef = [dithering,588,18,27]; @symbol_type<0> = [String,588,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 591; @symbol_moduledef = [device_info,591,15,26]; ]
        module device_info {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 591; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 593 18 ]
                symbol [ Toybox %tmp.107 593 32 38 ];
                %tmp.107 = getv ? :Toybox;
                symbol [ System %tmp.108 593 39 45 ];
                %tmp.108 = getv %tmp.107 :System;
                symbol [ SCREEN_SHAPE_ROUND %tmp.109 593 46 64 ];
                %tmp.109 = getv %tmp.108 :SCREEN_SHAPE_ROUND;
                putv self :screenShape %tmp.109;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 592; @position = 18; @symbol_constdef = [screenWidth,592,18,29]; @symbol_type<0> = [Number,592,33,39]; ]
            const screenWidth as Number = 416;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 593; @position = 18; @symbol_constdef = [screenShape,593,18,29]; ]
            const screenShape;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 594; @position = 18; @symbol_constdef = [hasNightMode,594,18,30]; @symbol_type<0> = [Boolean,594,34,41]; ]
            const hasNightMode as Boolean = false;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 595; @position = 18; @symbol_constdef = [hasGpu,595,18,24]; @symbol_type<0> = [Boolean,595,28,35]; ]
            const hasGpu as Boolean = true;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 596; @position = 18; @symbol_constdef = [screenHeight,596,18,30]; @symbol_type<0> = [Number,596,34,40]; ]
            const screenHeight as Number = 416;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 597; @position = 18; @symbol_constdef = [hasTouchScreen,597,18,32]; @symbol_type<0> = [Boolean,597,36,43]; ]
            const hasTouchScreen as Boolean = false;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 598; @position = 18; @symbol_constdef = [hasEnhancedReadabilityMode,598,18,44]; @symbol_type<0> = [Boolean,598,48,55]; ]
            const hasEnhancedReadabilityMode as Boolean = false;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 601; @symbol_moduledef = [confirmation_icon_dark__hint_confirm,601,15,51]; ]
        module confirmation_icon_dark__hint_confirm {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 601; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 602; @position = 18; @symbol_constdef = [filename,602,18,26]; @symbol_type<0> = [String,602,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_completion.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 603; @position = 18; @symbol_constdef = [dithering,603,18,27]; @symbol_type<0> = [String,603,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 606; @symbol_moduledef = [prompt_font__body_no_title,606,15,41]; ]
        module prompt_font__body_no_title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 606; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 607 18 ]
                symbol [ Graphics %tmp.110 607 34 42 ];
                %tmp.110 = getm $.Toybox.Graphics;
                symbol [ TEXT_JUSTIFY_CENTER %tmp.111 607 43 62 ];
                %tmp.111 = getv %tmp.110 :TEXT_JUSTIFY_CENTER;
                symbol [ Graphics %tmp.112 607 63 71 ];
                %tmp.112 = getm $.Toybox.Graphics;
                symbol [ TEXT_JUSTIFY_VCENTER %tmp.113 607 72 92 ];
                %tmp.113 = getv %tmp.112 :TEXT_JUSTIFY_VCENTER;
                %tmp.114 = bitor %tmp.111 %tmp.113;
                putv self :justification %tmp.114;
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 608 18 ]
                symbol [ Graphics %tmp.115 608 25 33 ];
                %tmp.115 = getm $.Toybox.Graphics;
                symbol [ FONT_TINY %tmp.116 608 34 43 ];
                %tmp.116 = getv %tmp.115 :FONT_TINY;
                putv self :font %tmp.116;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 607; @position = 18; @symbol_constdef = [justification,607,18,31]; ]
            const justification;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 608; @position = 18; @symbol_constdef = [font,608,18,22]; ]
            const font;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 611; @symbol_moduledef = [prompt_size__title_icon,611,15,38]; ]
        module prompt_size__title_icon {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 611; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 612; @position = 18; @symbol_constdef = [scaleX,612,18,24]; @symbol_type<0> = [Float,612,28,33]; ]
            const scaleX as Float = 0.15;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 613; @position = 18; @symbol_constdef = [scaleRelativeTo,613,18,33]; @symbol_type<0> = [String,613,37,43]; ]
            const scaleRelativeTo as String = "screen";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 616; @symbol_moduledef = [system_icon_dark__warning,616,15,40]; ]
        module system_icon_dark__warning {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 616; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 617; @position = 18; @symbol_constdef = [filename,617,18,26]; @symbol_type<0> = [String,617,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__warning.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 618; @position = 18; @symbol_constdef = [compress,618,18,26]; @symbol_type<0> = [String,618,30,36]; ]
            const compress as String = "true";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 619; @position = 18; @symbol_constdef = [dithering,619,18,27]; @symbol_type<0> = [String,619,31,37]; ]
            const dithering as String = "none";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 620; @position = 18; @symbol_constdef = [automaticPalette,620,18,34]; @symbol_type<0> = [String,620,38,44]; ]
            const automaticPalette as String = "true";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 623; @symbol_moduledef = [prompt_loc__title_icon,623,15,37]; ]
        module prompt_loc__title_icon {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 623; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 624; @position = 18; @symbol_constdef = [x,624,18,19]; @symbol_type<0> = [Number,624,23,29]; ]
            const x as Number = 208;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 625; @position = 18; @symbol_constdef = [y,625,18,19]; @symbol_type<0> = [Number,625,23,29]; ]
            const y as Number = 63;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 626; @position = 18; @symbol_constdef = [horizontalJustification,626,18,41]; @symbol_type<0> = [String,626,45,51]; ]
            const horizontalJustification as String = "center";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 627; @position = 18; @symbol_constdef = [verticalJustification,627,18,39]; @symbol_type<0> = [String,627,43,49]; ]
            const verticalJustification as String = "center";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 630; @symbol_moduledef = [prompt_font_enhanced_readability__title,630,15,54]; ]
        module prompt_font_enhanced_readability__title {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 630; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 631; @position = 18; @symbol_constdef = [exclude,631,18,25]; @symbol_type<0> = [Boolean,631,29,36]; ]
            const exclude as Boolean = true;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 634; @symbol_moduledef = [prompt_color_dark__body,634,15,38]; ]
        module prompt_color_dark__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 634; ]
            <init> {
[ "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen" 636 18 ]
                symbol [ Graphics %tmp.117 636 31 39 ];
                %tmp.117 = getm $.Toybox.Graphics;
                symbol [ COLOR_TRANSPARENT %tmp.118 636 40 57 ];
                %tmp.118 = getv %tmp.117 :COLOR_TRANSPARENT;
                putv self :background %tmp.118;
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 635; @position = 18; @symbol_constdef = [color,635,18,23]; @symbol_type<0> = [Number,635,27,33]; ]
            const color as Number = 0xFFFFFF;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 636; @position = 18; @symbol_constdef = [background,636,18,28]; ]
            const background;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 639; @symbol_moduledef = [confirmation_icon__hint_delete,639,15,45]; ]
        module confirmation_icon__hint_delete {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 639; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 640; @position = 18; @symbol_constdef = [filename,640,18,26]; @symbol_type<0> = [String,640,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\confirmation_icon__hint_delete.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 641; @position = 18; @symbol_constdef = [dithering,641,18,27]; @symbol_type<0> = [String,641,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 644; @symbol_moduledef = [system_icon_light__hint_button_right_top,644,15,55]; ]
        module system_icon_light__hint_button_right_top {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 644; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 645; @position = 18; @symbol_constdef = [filename,645,18,26]; @symbol_type<0> = [String,645,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon__hint_button_right_top.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 646; @position = 18; @symbol_constdef = [dithering,646,18,27]; @symbol_type<0> = [String,646,31,37]; ]
            const dithering as String = "none";
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 649; @symbol_moduledef = [confirmation_size__body,649,15,38]; ]
        module confirmation_size__body {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 649; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 650; @position = 18; @symbol_constdef = [width,650,18,23]; @symbol_type<0> = [Number,650,27,33]; ]
            const width as Number = 250;
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 651; @position = 18; @symbol_constdef = [height,651,18,24]; @symbol_type<0> = [Number,651,28,34]; ]
            const height as Number = 250;
        }
        [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 654; @symbol_moduledef = [system_icon_positive__check,654,15,42]; ]
        module system_icon_positive__check {
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 654; ]
            <init> {
            }
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 655; @position = 18; @symbol_constdef = [filename,655,18,26]; @symbol_type<0> = [String,655,30,36]; ]
            const filename as String = "C:\\Users\\<USER>\\AppData\\Roaming\\Garmin\\ConnectIQ\\Devices\\instinct3amoled50mm\\system_icon_positive__check.svg";
            [ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 656; @position = 18; @symbol_constdef = [dithering,656,18,27]; @symbol_type<0> = [String,656,31,37]; ]
            const dithering as String = "none";
        }
    }
}
[ @file = "d:\Melpa\Madre\bin\gen\006-B4587-00\source\Rez.mcgen"; @line = 1; ]
<init> {
}
