[ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 1; @symbol_importdef<0> = [Toybox,1,7,13]; @symbol_importdef<1> = [Application,1,14,25]; ]
import Toybox.Application;
[ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 2; @symbol_importdef<0> = [Toybox,2,7,13]; @symbol_importdef<1> = [Graphics,2,14,22]; ]
import Toybox.Graphics;
[ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 3; @symbol_importdef<0> = [Toybox,3,7,13]; @symbol_importdef<1> = [Lang,3,14,18]; ]
import Toybox.Lang;
[ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 4; @symbol_importdef<0> = [Toybox,4,7,13]; @symbol_importdef<1> = [WatchUi,4,14,21]; ]
import Toybox.WatchUi;
[ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 6; @symbol_classdef = [Background,6,6,16]; @symbol_extends<0> = [WatchUi,6,25,32]; @symbol_extends<1> = [Drawable,6,33,41]; ]
class Background extends WatchUi.Drawable {
    [ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 6; ]
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 6; ]
    static
    <init> {
    }
    [ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 8; @symbol_functiondef = [initialize,8,13,23]; ]
    function initialize() as Void {
D_Melpa_Madre_source_MadreBackground_mc_8_26_14_4_start:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 9 8 ]
        [ "D_Melpa_Madre_source_MadreBackground_mc_8_26_14_4_start" "D_Melpa_Madre_source_MadreBackground_mc_8_26_14_4_stop" ]
        %dictionary.1 = local;
        symbol [ dictionary %dictionary.1 9 12 22 ];
        %tmp.1 = newd 1;
        %tmp.3 = const :identifier;
        symbol [ identifier %tmp.3 10 13 23 const ];
        %tmp.4 = "Background";
        %tmp.5 = dup %tmp.1;
        %tmp.6 = aputv %tmp.5 %tmp.3 %tmp.4;
        lputv %dictionary.1 %tmp.6;
        symbol [ dictionary %dictionary.1 9 12 22 ];
[ "D:\Melpa\Madre\source\MadreBackground.mc" 13 8 ]
        symbol [ Drawable %tmp.8 13 8 16 ];
        %tmp.8 = getv ? :Drawable;
        symbol [ initialize %tmp.9 13 17 27 ];
        %tmp.9 = getv function %tmp.8 :initialize;
        %tmp.10 = lgetv %dictionary.1;
        symbol [ dictionary %tmp.10 13 28 38 ];
        invoke %tmp.8 %tmp.9(%tmp.10);
D_Melpa_Madre_source_MadreBackground_mc_8_26_14_4_stop:
    }
    [ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 16; @symbol_functiondef = [draw,16,13,17]; @symbol_param<0> = [dc,16,18,20]; @symbol_param<0>_type<0> = [Graphics,16,24,32]; @symbol_param<0>_type<1> = [Dc,16,33,35]; ]
    function draw(dc as Graphics.Dc) as Void {
D_Melpa_Madre_source_MadreBackground_mc_16_45_38_4_start:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 18 8 ]
        [ "D_Melpa_Madre_source_MadreBackground_mc_16_45_38_4_start" "D_Melpa_Madre_source_MadreBackground_mc_16_45_38_4_stop" ]
        %backgroundColor.1 = local;
        symbol [ backgroundColor %backgroundColor.1 18 12 27 ];
        symbol [ Graphics %tmp.1 18 30 38 ];
        %tmp.1 = getm $.Toybox.Graphics;
        symbol [ COLOR_BLACK %tmp.2 18 39 50 ];
        %tmp.2 = getv %tmp.1 :COLOR_BLACK;
        lputv %backgroundColor.1 %tmp.2;
        symbol [ backgroundColor %backgroundColor.1 18 12 27 ];
[ "D:\Melpa\Madre\source\MadreBackground.mc" 21 8 ]
D_Melpa_Madre_source_MadreBackground_mc_21_8_33_8_if_stmt:
        symbol [ Application %tmp.3 21 12 23 ];
        %tmp.3 = getm $.Toybox.Application;
        %tmp.5 = const :Properties;
        symbol [ Properties %tmp.5 21 29 39 const ];
        %tmp.6 = canhazplz %tmp.3 %tmp.5;
        bf %tmp.6 @D_Melpa_Madre_source_MadreBackground_mc_21_8_33_8_if_false;
D_Melpa_Madre_source_MadreBackground_mc_21_8_33_8_if_true:
D_Melpa_Madre_source_MadreBackground_mc_21_41_26_8_start:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 22 12 ]
        [ "D_Melpa_Madre_source_MadreBackground_mc_21_41_26_8_start" "D_Melpa_Madre_source_MadreBackground_mc_21_41_26_8_stop" ]
        %bgColor.2 = local;
        symbol [ bgColor %bgColor.2 22 16 23 ];
        symbol [ Application %tmp.7 22 26 37 ];
        %tmp.7 = getm $.Toybox.Application;
        symbol [ Properties %tmp.8 22 38 48 ];
        %tmp.8 = getv %tmp.7 :Properties;
        symbol [ getValue %tmp.9 22 49 57 ];
        %tmp.9 = getv function %tmp.8 :getValue;
        %tmp.10 = "BackgroundColor";
        %tmp.11 = invoke %tmp.8 %tmp.9(%tmp.10);
        lputv %bgColor.2 %tmp.11;
        symbol [ bgColor %bgColor.2 22 16 23 ];
[ "D:\Melpa\Madre\source\MadreBackground.mc" 23 12 ]
D_Melpa_Madre_source_MadreBackground_mc_23_12_25_12_if_stmt:
        %tmp.12 = lgetv %bgColor.2;
        symbol [ bgColor %tmp.12 23 16 23 ];
        %tmp.13 = null;
        %tmp.14 = ne %tmp.12 %tmp.13;
        bf %tmp.14 @D_Melpa_Madre_source_MadreBackground_mc_23_12_25_12_if_end;
D_Melpa_Madre_source_MadreBackground_mc_23_12_25_12_if_true:
D_Melpa_Madre_source_MadreBackground_mc_23_33_25_12_start:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 24 16 ]
        %tmp.15 = lgetv %bgColor.2;
        symbol [ bgColor %tmp.15 24 34 41 ];
        %tmp.16 = as %tmp.15 { (!Null) };
        %tmp.17 = as %tmp.16 Number;
        symbol [ Number %tmp.17 24 45 51 ];
        lputv %backgroundColor.1 %tmp.17;
        symbol [ backgroundColor %backgroundColor.1 24 16 31 ];
D_Melpa_Madre_source_MadreBackground_mc_23_33_25_12_stop:
        goto @D_Melpa_Madre_source_MadreBackground_mc_23_12_25_12_if_end;
D_Melpa_Madre_source_MadreBackground_mc_23_12_25_12_if_end:
D_Melpa_Madre_source_MadreBackground_mc_21_41_26_8_stop:
        goto @D_Melpa_Madre_source_MadreBackground_mc_21_8_33_8_if_end;
D_Melpa_Madre_source_MadreBackground_mc_21_8_33_8_if_false:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 28 13 ]
D_Melpa_Madre_source_MadreBackground_mc_28_13_33_8_if_stmt:
        symbol [ Application %tmp.18 28 17 28 ];
        %tmp.18 = getm $.Toybox.Application;
        %tmp.20 = const :Storage;
        symbol [ Storage %tmp.20 28 34 41 const ];
        %tmp.21 = canhazplz %tmp.18 %tmp.20;
        bf %tmp.21 @D_Melpa_Madre_source_MadreBackground_mc_28_13_33_8_if_end;
D_Melpa_Madre_source_MadreBackground_mc_28_13_33_8_if_true:
D_Melpa_Madre_source_MadreBackground_mc_28_43_33_8_start:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 29 12 ]
        [ "D_Melpa_Madre_source_MadreBackground_mc_28_43_33_8_start" "D_Melpa_Madre_source_MadreBackground_mc_28_43_33_8_stop" ]
        %bgColor.3 = local;
        symbol [ bgColor %bgColor.3 29 16 23 ];
        symbol [ Application %tmp.22 29 26 37 ];
        %tmp.22 = getm $.Toybox.Application;
        symbol [ Storage %tmp.23 29 38 45 ];
        %tmp.23 = getv %tmp.22 :Storage;
        symbol [ getValue %tmp.24 29 46 54 ];
        %tmp.24 = getv function %tmp.23 :getValue;
        %tmp.25 = "BackgroundColor";
        %tmp.26 = invoke %tmp.23 %tmp.24(%tmp.25);
        lputv %bgColor.3 %tmp.26;
        symbol [ bgColor %bgColor.3 29 16 23 ];
[ "D:\Melpa\Madre\source\MadreBackground.mc" 30 12 ]
D_Melpa_Madre_source_MadreBackground_mc_30_12_32_12_if_stmt:
        %tmp.27 = lgetv %bgColor.3;
        symbol [ bgColor %tmp.27 30 16 23 ];
        %tmp.28 = null;
        %tmp.29 = ne %tmp.27 %tmp.28;
        bf %tmp.29 @D_Melpa_Madre_source_MadreBackground_mc_30_12_32_12_if_end;
D_Melpa_Madre_source_MadreBackground_mc_30_12_32_12_if_true:
D_Melpa_Madre_source_MadreBackground_mc_30_33_32_12_start:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 31 16 ]
        %tmp.30 = lgetv %bgColor.3;
        symbol [ bgColor %tmp.30 31 34 41 ];
        %tmp.31 = as %tmp.30 { (!Null) };
        %tmp.32 = as %tmp.31 Number;
        symbol [ Number %tmp.32 31 45 51 ];
        lputv %backgroundColor.1 %tmp.32;
        symbol [ backgroundColor %backgroundColor.1 31 16 31 ];
D_Melpa_Madre_source_MadreBackground_mc_30_33_32_12_stop:
        goto @D_Melpa_Madre_source_MadreBackground_mc_30_12_32_12_if_end;
D_Melpa_Madre_source_MadreBackground_mc_30_12_32_12_if_end:
D_Melpa_Madre_source_MadreBackground_mc_28_43_33_8_stop:
        goto @D_Melpa_Madre_source_MadreBackground_mc_28_13_33_8_if_end;
D_Melpa_Madre_source_MadreBackground_mc_28_13_33_8_if_end:
D_Melpa_Madre_source_MadreBackground_mc_21_8_33_8_if_end:
[ "D:\Melpa\Madre\source\MadreBackground.mc" 36 8 ]
        %tmp.33 = lgetv %dc;
        symbol [ dc %tmp.33 36 8 10 ];
        symbol [ setColor %tmp.34 36 11 19 ];
        %tmp.34 = getv function %tmp.33 :setColor;
        symbol [ Graphics %tmp.35 36 20 28 ];
        %tmp.35 = getm $.Toybox.Graphics;
        symbol [ COLOR_TRANSPARENT %tmp.36 36 29 46 ];
        %tmp.36 = getv %tmp.35 :COLOR_TRANSPARENT;
        %tmp.37 = lgetv %backgroundColor.1;
        symbol [ backgroundColor %tmp.37 36 48 63 ];
        invoke %tmp.33 %tmp.34(%tmp.36, %tmp.37);
[ "D:\Melpa\Madre\source\MadreBackground.mc" 37 8 ]
        %tmp.38 = lgetv %dc;
        symbol [ dc %tmp.38 37 8 10 ];
        symbol [ clear %tmp.39 37 11 16 ];
        %tmp.39 = getv function %tmp.38 :clear;
        invoke %tmp.38 %tmp.39();
D_Melpa_Madre_source_MadreBackground_mc_16_45_38_4_stop:
    }
}
[ @file = "D:\Melpa\Madre\source\MadreBackground.mc"; @line = 1; ]
<init> {
}
